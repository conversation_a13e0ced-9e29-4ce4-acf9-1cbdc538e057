import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { createDateFilter, innovatioIdeaStatus } from '../../utils/util';
import { logger } from '../../utils/logger';
import { simpleTriggers } from '../reward/simpleTriggers';
import { formatString } from '../../utils/stringFormatter';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../../utils/cache';

export interface UpdateInnovationIdeaData {
  title?: string;
  description?: string;
  ideaId: number;
  status?: 'DRAFT' | 'PENDING_REVIEW' | 'ACCEPTED' | 'REJECTED' | 'IMPLEMENTED';
}

export const innovationIdeaService = {
  createInnovationIdea: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.HUB_CREATE);

      // Check if staff is blacklisted for innovation ideas
      const staff = await db.staff.findUnique({
        where: { id: staffId },
        include: { blacklist: true },
      });

      const hasActiveBlacklist = staff?.blacklist?.some(
        (bl) => bl.action === 'INNOVATION_IDEA_POSTED' && bl.isActive
      );

      if (hasActiveBlacklist) {
        throw new HttpError(
          'You are restricted from creating innovation ideas. Kindly reach out to the administrator.',
          403
        );
      }

      const { title, description, categoryId, tagIds } = reqBody;
      if (!title || !description) {
        throw new HttpError('Title and description are required', 400);
      }
      const existingIdea = await db.innovationIdea.findFirst({
        where: { title: title.trim() },
      });
      if (existingIdea) {
        throw new HttpError('Idea with this title already exists', 400);
      }
      const existingDraftIdea = await db.innovationIdea.findFirst({
        where: { authorId: staffId, status: 'DRAFT' },
      });
      if (existingDraftIdea) {
        throw new HttpError('You can only have one draft idea at a time', 400);
      }
      const innovationIdea = await db.innovationIdea.create({
        data: {
          title,
          slug: formatString.formatSlug(title),
          description,
          status: 'DRAFT',
          authorId: staffId,
          categoryId: Number(categoryId),
          tags: {
            connect: tagIds.map((tagId: number) => ({
              id: Number(tagId),
            })),
          },
        },
      });

      // Trigger reward for posting innovation idea (simplified)
      try {
        await simpleTriggers.onInnovationIdeaPosted(staffId, innovationIdea.id);
      } catch (rewardError) {
        logger.error('Error triggering innovation idea reward:', rewardError);
        // Don't fail the main operation if reward fails
      }

      // Clear all innovation-related caches
      await Promise.all([
        deleteCacheByPattern('innovation:ideas:*'),
        deleteCacheByPattern('innovation:leaderboard:*'),
        deleteCacheByPattern('staff:*:total_points'),
        deleteCacheByPattern('staff:*:monthly:*'),
      ]);

      return {
        message: 'Innovation idea submitted successfully',
      };
    } catch (error) {
      logger.error('Error submitting innovation idea:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create innovation idea', 400);
    }
  },

  getAllCategories: async (staffId: number, query: any) => {
    try {
      return db.innovationCategory.findMany({
        select: {
          id: true,
          name: true,
        },
      });
    } catch (error) {
      logger.error('Error getting innovation categories:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch innovation categories', 400);
    }
  },

  getAllTags: async (staffId: number, query: any) => {
    try {
      return db.innovationTag.findMany({
        select: {
          id: true,
          name: true,
        },
      });
    } catch (error) {
      logger.error('Error getting innovation tags:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch innovation tags', 400);
    }
  },

  getInnovationIdeasStats: async (staffId: number, query: any) => {
    try {
      const [totalCount, groupedCounts] = await db.$transaction([
        db.innovationIdea.count({ where: { deleted: false } }),
        db.innovationIdea.groupBy({
          by: ['status'],
          where: { deleted: false },
          orderBy: {
            status: 'asc',
          },
          _count: true,
        }),
      ]);

      const stats: Record<string, number> = {
        total: totalCount,
      };

      for (const item of groupedCounts) {
        const statusKey = item.status.toLowerCase();
        stats[statusKey] = item._count as number;
      }

      return stats;
    } catch (error) {
      logger.error('Error getting innovation ideas stats:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch innovation ideas stats', 400);
    }
  },

  getAllInnovationIdeas: async (staffId: number, query: any) => {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        search,
        slug,
        startDate,
        endDate,
        singleDate,
        myIdeas,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const isMyIdeas = myIdeas === 'true';

      if (status && !innovatioIdeaStatus.includes(status.toUpperCase())) {
        throw new HttpError('Invalid status value', 400);
      }

      // Generate cache key based on query parameters
      const cacheKey = `innovation:ideas:${JSON.stringify({ page, limit, status, search, slug, startDate, endDate, singleDate, myIdeas: isMyIdeas ? staffId : null, sortBy, sortOrder })}`;

      // Try cache first
      const cached = await getCache(cacheKey);
      if (cached) {
        return cached;
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);
      const dateFilter = createDateFilter(
        startDate,
        endDate,
        'createdAt',
        singleDate
      );

      const whereClause: any = {
        ...(status ? { status: status.toUpperCase() } : {}),
        ...(search
          ? {
              OR: [
                { title: { contains: search, mode: 'insensitive' } },
                { description: { contains: search, mode: 'insensitive' } },
                {
                  author: {
                    fullName: { contains: search, mode: 'insensitive' },
                  },
                },
                {
                  category: {
                    name: { contains: search, mode: 'insensitive' },
                  },
                },
                {
                  tags: {
                    some: {
                      name: { contains: search, mode: 'insensitive' },
                    },
                  },
                },
              ],
            }
          : {}),
        deleted: false,
        ...(slug && { slug }),
        ...dateFilter,
        ...(isMyIdeas ? { authorId: staffId } : {}),
      };

      const [ideas, total] = await Promise.all([
        db.innovationIdea.findMany({
          where: whereClause,
          skip,
          take,
          orderBy: {
            [sortBy]: sortOrder,
          },
          include: {
            author: {
              select: {
                id: true,
                fullName: true,
                email: true,
                department: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            comments: {
              select: {
                id: true,
                content: true,
                createdAt: true,
                staff: {
                  select: {
                    fullName: true,
                  },
                },
              },
            },
            tags: {
              select: {
                id: true,
                name: true,
              },
            },
            category: {
              select: {
                id: true,
                name: true,
              },
            },
            likes: {
              where: { liked: true },
              select: {
                id: true,
                staff: {
                  select: {
                    fullName: true,
                  },
                },
              },
            },
          },
        }),
        db.innovationIdea.count({ where: whereClause }),
      ]);

      const result = {
        ideas,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / take),
          totalItems: total,
          itemsPerPage: take,
        },
      };

      // Cache for 2 minutes
      await setCache(cacheKey, result, 120);
      return result;
    } catch (error) {
      logger.error('Error getting innovation ideas:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch innovation ideas', 400);
    }
  },

  updateInnovationIdea: async (
    staffId: number,
    reqBody: UpdateInnovationIdeaData
  ) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.HUB_EDIT);
      const idea = await db.innovationIdea.findUnique({
        where: { id: Number(reqBody.ideaId) },
      });

      if (!idea) {
        throw new HttpError('Innovation idea not found', 404);
      }

      const updateData: any = {};

      if (reqBody.title) {
        updateData.title = reqBody.title.trim();
      }

      if (reqBody.description) {
        updateData.description = reqBody.description.trim();
      }

      if (reqBody.status) {
        updateData.status = reqBody.status;

        // Set appropriate timestamps based on status
        if (reqBody.status === 'PENDING_REVIEW' && !idea.reviewedAt) {
          updateData.reviewedAt = new Date();
        } else if (reqBody.status === 'ACCEPTED' && !idea.acceptedAt) {
          updateData.acceptedAt = new Date();
        } else if (reqBody.status === 'REJECTED' && !idea.rejectedAt) {
          updateData.rejectedAt = new Date();
        } else if (reqBody.status === 'IMPLEMENTED' && !idea.implementedAt) {
          updateData.implementedAt = new Date();
        }
      }

      const updatedIdea = await db.innovationIdea.update({
        where: { id: Number(reqBody.ideaId) },
        data: updateData,
      });

      // Trigger reward if idea was accepted
      if (reqBody.status === 'ACCEPTED' && idea.status !== 'ACCEPTED') {
        try {
          await simpleTriggers.onInnovationIdeaAccepted(idea.authorId, idea.id);
        } catch (rewardError) {
          logger.error(
            'Error triggering innovation idea acceptance reward:',
            rewardError
          );
          // Don't fail the main operation if reward fails
        }
      }

      // Clear all innovation-related caches
      await Promise.all([
        deleteCacheByPattern('innovation:ideas:*'),
        deleteCacheByPattern('innovation:leaderboard:*'),
        deleteCacheByPattern('staff:*:total_points'),
        deleteCacheByPattern('staff:*:monthly:*'),
      ]);

      return {
        message: 'Innovation idea updated successfully',
        data: updatedIdea,
      };
    } catch (error) {
      logger.error('Error updating innovation idea:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update innovation idea', 400);
    }
  },

  deleteInnovationIdea: async (staffId: number, ideaId: number) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.HUB_EDIT);
      const idea = await db.innovationIdea.findUnique({
        where: { id: ideaId },
      });

      if (!idea) {
        throw new HttpError('Innovation idea not found', 404);
      }

      // Remove all reward logs and mark idea as deleted
      await db.$transaction(async (tx) => {
        // Delete reward logs
        await tx.rewardEventLog.deleteMany({
          where: {
            entityId: ideaId,
            eventType: {
              in: [
                'INNOVATION_IDEA_POSTED',
                'INNOVATION_IDEA_COMMENT',
                'INNOVATION_IDEA_LIKE',
                'INNOVATION_IDEA_ACCEPTED',
              ],
            },
          },
        });

        // Mark idea as deleted
        await tx.innovationIdea.update({
          where: { id: ideaId },
          data: { deleted: true },
        });
      });

      // Clear all innovation-related caches
      await Promise.all([
        deleteCacheByPattern('innovation:ideas:*'),
        deleteCacheByPattern('innovation:leaderboard:*'),
        deleteCacheByPattern('staff:*:total_points'),
        deleteCacheByPattern('staff:*:monthly:*'),
      ]);

      return {
        message: 'Innovation idea deleted successfully',
      };
    } catch (error) {
      logger.error('Error deleting innovation idea:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete innovation idea', 400);
    }
  },

  getInnovationLeaderboard: async (staffId: number, query: any = {}) => {
    try {
      const now = new Date();
      const month = query.month ? parseInt(query.month) : now.getMonth() + 1;
      const year = query.year ? parseInt(query.year) : now.getFullYear();

      const leaderboardKey = `innovation:leaderboard:${year}:${month}`;
      const staffTotalKey = `staff:${staffId}:total_points`;
      const staffMonthlyKey = `staff:${staffId}:monthly:${year}:${month}`;

      // Try cache first
      const [cachedLeaderboard, cachedStaffTotal, cachedStaffMonthly] =
        await Promise.all([
          getCache(leaderboardKey),
          getCache(staffTotalKey),
          getCache(staffMonthlyKey),
        ]);

      let rankedIdeas, staffTotalPoints, staffMonthlyPoints;
      const eventTypes: (
        | 'INNOVATION_IDEA_POSTED'
        | 'INNOVATION_IDEA_COMMENT'
        | 'INNOVATION_IDEA_LIKE'
        | 'INNOVATION_IDEA_ACCEPTED'
      )[] = [
        'INNOVATION_IDEA_POSTED',
        'INNOVATION_IDEA_COMMENT',
        'INNOVATION_IDEA_LIKE',
        'INNOVATION_IDEA_ACCEPTED',
      ];

      // Get leaderboard data
      if (cachedLeaderboard) {
        rankedIdeas = cachedLeaderboard;
      } else {
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0, 23, 59, 59, 999);

        const topIdeas = await db.rewardEventLog.groupBy({
          by: ['entityId'],
          where: {
            receivedAt: { gte: startDate, lte: endDate },
            eventType: { in: eventTypes },
            valueType: 'POINTS',
            entityId: { not: null },
          },
          _sum: { value: true },
          orderBy: { _sum: { value: 'desc' } },
          take: 5,
        });

        const ideaIds = topIdeas
          .map((t) => t.entityId)
          .filter((id): id is number => id !== null);
        const ideas = await db.innovationIdea.findMany({
          where: {
            id: { in: ideaIds },
            deleted: false,
          },
          include: {
            author: {
              select: {
                id: true,
                fullName: true,
                department: { select: { name: true } },
              },
            },
            comments: {
              select: {
                id: true,
                content: true,
                createdAt: true,
                staff: { select: { fullName: true } },
              },
            },
            likes: {
              where: { liked: true },
              select: { id: true, staff: { select: { fullName: true } } },
            },
            category: { select: { name: true } },
          },
        });

        rankedIdeas = topIdeas
          .map((topIdea) => {
            const idea = ideas.find((i) => i.id === topIdea.entityId);
            if (!idea) return null;
            return {
              ...idea,
              commentsCount: idea.comments.length,
              likesCount: idea.likes.length,
              rewardPoints: Number(topIdea._sum.value || 0),
            };
          })
          .filter((idea): idea is NonNullable<typeof idea> => idea !== null);

        await setCache(leaderboardKey, rankedIdeas, 300); // 5min cache
      }

      if (cachedStaffTotal && cachedStaffMonthly) {
        staffTotalPoints = cachedStaffTotal;
        staffMonthlyPoints = cachedStaffMonthly;
      } else {
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0, 23, 59, 59, 999);

        const [totalResult, monthlyResult] = await Promise.all([
          cachedStaffTotal
            ? Promise.resolve({ _sum: { value: cachedStaffTotal } })
            : db.rewardEventLog.aggregate({
                where: {
                  staffId,
                  eventType: { in: eventTypes },
                  valueType: 'POINTS',
                },
                _sum: { value: true },
              }),
          cachedStaffMonthly
            ? Promise.resolve({ _sum: { value: cachedStaffMonthly } })
            : db.rewardEventLog.aggregate({
                where: {
                  staffId,
                  receivedAt: { gte: startDate, lte: endDate },
                  eventType: { in: eventTypes },
                  valueType: 'POINTS',
                },
                _sum: { value: true },
              }),
        ]);

        staffTotalPoints = Number(totalResult._sum?.value || 0);
        staffMonthlyPoints = Number(monthlyResult._sum?.value || 0);

        if (!cachedStaffTotal)
          await setCache(staffTotalKey, staffTotalPoints, 3600);
        if (!cachedStaffMonthly)
          await setCache(staffMonthlyKey, staffMonthlyPoints, 3600);
      }

      return {
        ideas: rankedIdeas,
        staffPoints: {
          totalPoints: staffTotalPoints,
          monthlyPoints: staffMonthlyPoints,
          month,
          year,
        },
      };
    } catch (error) {
      logger.error('Error getting innovation leaderboard:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch innovation leaderboard', 400);
    }
  },
};
