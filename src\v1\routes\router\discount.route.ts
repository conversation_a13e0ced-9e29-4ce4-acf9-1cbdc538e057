import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { priceModifierControllers } from '../../controllers/discount/discount.controller';

export const discountRoute = Router();

discountRoute.post(
  '/new-discount',
  secure,
  priceModifierControllers.CreateDiscountHandler
);

discountRoute.post(
  '/request-discount-refund',
  priceModifierControllers.DiscountAndRefundRequestHandler
);

discountRoute.get(
  '/list-discounts',
  secure,
  priceModifierControllers.ListDiscountHandler
);

discountRoute.get(
  '/list-discount-records',
  secure,
  priceModifierControllers.ListDiscountRecordHandler
);

discountRoute.post(
  '/deactivate-expired-modifiers',
  secure,
  priceModifierControllers.DeactivateExpiredModifiersHandler
);

discountRoute.patch(
  '/update-discount',
  secure,
  priceModifierControllers.UpdateDiscountHandler
);
