import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { createDateFilter } from '../../utils/util';
import { logger } from '../../utils/logger';
import { userService } from '../booking/user';
import { settingsService } from '../settings';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import {
  generateCode,
  timestamp,
  formatDate,
  numberFormat,
} from '../../utils/util';



export const dicountAndRefundService = {
  create: async (reqBody: any) => {
    try {
      const {
        type,
        name,
        location,
        amount,
        totalBill,
        motivation,
         bankName,
      accountName,
      accountNumber,
        ...userData
      } = reqBody;
      const user = await userService.checkAndCreateUser(userData);

      const allowedTypes = ['REFUND', 'DISCOUNT'];
      const normalizedType = type.toUpperCase();
      if (!allowedTypes.includes(normalizedType)) {
        throw new HttpError('Invalid type', 400);
      }

      const existingRequest = await db.discountAndRefund.findFirst({
        where: {
          userId: user.id,
          status: 'PENDING',
          type: normalizedType,
        },
      });

      if (existingRequest) {
        throw new HttpError(
          `A yet to be processed ${type} request already exists for this user.`, 400
        );
      }

      // Validate numeric fields to prevent overflow
      const maxValue = ********.99;
      if (amount && amount > maxValue) {
        throw new HttpError('Amount exceeds maximum allowed value', 400);
      }

      const reference = `${normalizedType === 'REFUND' ? 'RFD' : 'DSC'}-${timestamp()}${generateCode(5)?.toUpperCase()}`;
      await db.discountAndRefund.create({
        data: {
          userId: user.id,
          reference,
          type: normalizedType,
          locationId: Number(location),
          motivation,
          requestedAmount: amount,
          totalBill,
          ...{
            ...(normalizedType === 'REFUND' && {
              bankName,
              accountName,
              accountNumber,
            }),
          },
        },
      });

             try {
                const adminEmails =
                  await settingsService.getAdminNotificationEmails();
                  const type = normalizedType === 'REFUND' ? 'refund request' : 'discount request';
                  const title = normalizedType === 'REFUND' ? 'Refund Request Received' : 'Discount Request Received';
                  const action = normalizedType === 'REFUND' ? 'Process Refund' : 'Review Discount';
                if (adminEmails?.length > 0) {
                  enqueueSendEmailJob({
                    from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
                    to: adminEmails.join(', '),
                    subject: `URGENT: ${title}`,
                    template: 'prompt',
                    context: {
                      type: type,
                      title: title,
                      description: motivation,
                      customerName: user.firstName,
                      customerEmail: user.emailAddress,
                      actionText:action, 
                      amount: numberFormat(reqBody.amount),
                      date: formatDate(new Date(), true),
                    },
                  });
                }
              } catch (error) {
                logger.error('Error sending admin emails:', error);
              }

      return { message: 'Request sent successfully' };
    } catch (error) {
      logger.error('Error creating request:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create request', 400);
    }
  },

  updatePriceModifier: async (staffId: any, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REWARD_EDIT);
      const { id, amount, endDate, isActive, packages } = reqBody;
      const codeExist = await db.packagePriceModifier.findUnique({
        where: {
          id: id,
        },
      });

      if (!codeExist) {
        throw new HttpError('Discount code does not exist', 400);
      }

      const updateData: any = {};

      if (amount !== undefined) updateData.amount = amount;
      if (endDate !== undefined) updateData.endDate = endDate;
      if (isActive !== undefined) updateData.isActive = isActive;
      if (packages !== undefined && Array.isArray(packages)) {
        updateData.packageLocationPrice = {
          set: [],
          connect: packages.map((pkgId: any) => ({ id: Number(pkgId) })),
        };
      }

      await db.packagePriceModifier.update({
        where: { id },
        data: updateData,
      });

      return { message: 'Discount updated successfully' };
    } catch (error) {
      logger.error('Error updating price modifier:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update price modifier', 400);
    }
  },

  getAllDiscountRecords: async (staffId: any, query: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.TRANSACTION_EDIT);

      const page: number = parseInt(query.page as string) || 1;
      const limit: number = parseInt(query.limit as string) || 10;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const singleDate = query.singleDate as string;
      const search = query.search as string;
      const status = query.status as string;
      const type = query.type as string;

      const dateFilter = createDateFilter(
        startDate,
        endDate,
        'createdAt',
        singleDate
      );
      const whereClause: any = {
        ...(search
          ? {
              OR: [
                { reference: { contains: search, mode: 'insensitive' } },
                {
                  user: {
                    OR: [
                      {
                        emailAddress: { contains: search, mode: 'insensitive' },
                      },
                      {
                        phoneNumber: { contains: search, mode: 'insensitive' },
                      },
                    ],
                  },
                },
              ],
            }
          : {}),
        ...(status ? { status } : {}),
        ...(type ? { type: type.toUpperCase() } : {}),
        ...dateFilter,
      };

      const [result, totalPages, totalCount] = await db.$transaction([
        db.discountAndRefund.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
          include: {
            user: {
              select: {
                emailAddress: true,
                phoneNumber: true,
              },
            },
          },
        }),
        db.discountAndRefund.count({
          where: whereClause,
        }),
        db.discountAndRefund.count(),
      ]);

      return {
        result,
        totalPages: Math.ceil(totalPages / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
      };
    } catch (error) {
      logger.error('Error getting all discount and refund records:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch discount and refund records', 400);
    }
  },

};
