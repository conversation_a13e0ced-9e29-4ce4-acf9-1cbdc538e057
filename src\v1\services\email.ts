import config from '../../config/app.config';
import nodemailer from 'nodemailer';
import nodemailerExpressHandlebars, {
  NodemailerExpressHandlebarsOptions,
} from 'nodemailer-express-handlebars';
import SMTPTransport from 'nodemailer/lib/smtp-transport';
import path from 'path';
import { logger } from '../utils/logger';

// Create a more robust email service with fallback options

// Primary SMTP Configuration
const primarySmtpOptions: SMTPTransport.Options = {
  host: 'mail.cedarcresthospitals.com',
  secure: false,
  port: 587,
  tls: {
    minVersion: 'TLSv1.2',
    rejectUnauthorized: false,
  },
  auth: {
    user: config.NODEMAILER_EMAIL,
    pass: config.NODEMAILER_EMAIL_PASSWORD,
  },
  // connectionTimeout: 5000, // 5 seconds
  greetingTimeout: 5000, // 5 seconds
  socketTimeout: 10000, // 10 seconds
};

// Fallback to Ethereal (test) SMTP in development mode
let fallbackTransporter: nodemailer.Transporter<any> | null = null;

// Create a test account for development mode
const createTestAccount = async () => {
  if (config.NODE_ENV !== 'production') {
    try {
      logger.info('Creating test email account for development mode...');
      const testAccount = await nodemailer.createTestAccount();

      const testSmtpOptions = {
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
          user: testAccount.user,
          pass: testAccount.pass,
        },
      };

      fallbackTransporter = nodemailer.createTransport(testSmtpOptions);
      logger.info(
        `Test email account created: email-${testAccount.user}, password-${testAccount.pass}`
      );
      return true;
    } catch (error) {
      logger.error('Failed to create test email account:', error);
      return false;
    }
  }
  return false;
};

// Initialize test account for development
createTestAccount();

// Create primary transporter
const primaryTransporter = nodemailer.createTransport(primarySmtpOptions);

// Verify transporter connection
primaryTransporter
  .verify()
  .then(() => {
    logger.info('Primary SMTP connection verified successfully');
  })
  .catch((error) => {
    logger.error('Primary SMTP connection verification failed:', error);
    logger.info('Will use fallback email service if available');
  });

// Handlebars Configuration
const handlebarsOptions: NodemailerExpressHandlebarsOptions = {
  viewEngine: {
    partialsDir: path.resolve(__dirname, 'emails'),
    defaultLayout: '',
  },
  viewPath: path.resolve(__dirname, 'emails'),
};

// Use Handlebars with Nodemailer
primaryTransporter.use(
  'compile',
  nodemailerExpressHandlebars(handlebarsOptions)
);

// Apply handlebars to fallback transporter if available
const setupFallbackHandlebars = () => {
  if (fallbackTransporter) {
    try {
      fallbackTransporter.use(
        'compile',
        nodemailerExpressHandlebars(handlebarsOptions)
      );
      logger.info('Handlebars configured for fallback email transporter');
    } catch (error) {
      logger.error(
        'Failed to configure handlebars for fallback transporter:',
        error
      );
    }
  }
};

// Set up fallback handlebars after fallback transporter is initialized
setTimeout(setupFallbackHandlebars, 1000);

// Function to Send Email with fallback and better error handling
const sendMail = async (mailOptions: any) => {
  logger.info(`Attempting to send email to: ${mailOptions.to}`);

  try {
    // Add a timeout to the email sending operation
    const emailPromise = primaryTransporter.sendMail(mailOptions);

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Email sending timed out after 15 seconds'));
      }, 15000); // 15 second timeout
    });

    // Race the email sending against the timeout
    const info = (await Promise.race([emailPromise, timeoutPromise])) as any;

    logger.info(`Email sent successfully via primary SMTP: ${info.response}`);

    // If in development, provide a link to preview the email
    if (config.NODE_ENV !== 'production' && info.messageId) {
      logger.info(`Preview URL: ${nodemailer.getTestMessageUrl(info)}`);
    }

    return info;
  } catch (primaryError) {
    logger.error('Error sending email via primary SMTP:', primaryError);

    // Try fallback transporter if available
    if (fallbackTransporter) {
      logger.info('Attempting to send via fallback email service...');
      try {
        const fallbackInfo = await fallbackTransporter.sendMail(mailOptions);
        logger.info(
          `Email sent via fallback service: ${fallbackInfo.response}`
        );

        // Provide preview URL for test emails
        if (fallbackInfo.messageId) {
          logger.info(
            `Preview URL: ${nodemailer.getTestMessageUrl(fallbackInfo)}`
          );
        }

        return fallbackInfo;
      } catch (fallbackError) {
        logger.error('Fallback email service also failed:', fallbackError);
        const primaryMsg =
          primaryError instanceof Error
            ? primaryError.message
            : String(primaryError);
        const fallbackMsg =
          fallbackError instanceof Error
            ? fallbackError.message
            : String(fallbackError);
        throw new Error(
          `Primary email error: ${primaryMsg}, Fallback error: ${fallbackMsg}`
        );
      }
    } else {
      // No fallback available, throw the original error
      throw primaryError;
    }
  }
};

// Export the email service
export default sendMail;
