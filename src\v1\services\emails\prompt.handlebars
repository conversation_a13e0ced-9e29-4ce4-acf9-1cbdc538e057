<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{title}}</title>

    <style type="text/css" emogrify="no">
      body, table, td {
        font-family: Arial, sans-serif;
      }
      table td {
        border-collapse: collapse;
      }
      img {
        border: 0;
        outline: none;
        text-decoration: none;
        -ms-interpolation-mode: bicubic;
      }
      body {
        margin: 0;
        padding: 0;
        background-color: #f2f2f2;
        color: #333;
      }

      /* Unified red theme */
      .urgent-header {
        background-color: #ff3b30;
        color: #fff;
        text-align: center;
        padding: 20px;
      }
      .badge {
        display: inline-block;
        background-color: #ff3b30;
        color: #fff;
        font-weight: bold;
        border-radius: 4px;
        padding: 5px 10px;
        margin-bottom: 15px;
      }
      .info-box {
        background-color: #fff9f9;
        border: 2px solid #ff3b30;
        border-left: 8px solid #ff3b30;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
      }
      @media only screen and (max-width: 480px) {
        .container {
          width: 100% !important;
        }
        img {
          max-width: 100% !important;
          height: auto !important;
        }
      }
    </style>
  </head>

  <body>
    <table cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <table
            cellpadding="0"
            cellspacing="0"
            width="600"
            class="container"
            style="background-color: #fff;"
          >
            <!-- Header -->
            <tr>
              <td class="urgent-header">
                <h1 style="margin: 0;">{{title}}</h1>
              </td>
            </tr>

            <!-- Content -->
            <tr>
              <td style="padding: 20px;">
                <div class="badge">{{badgeText}}</div>

                <p><strong>Hello {{recipientRole}},</strong></p>

                <div class="info-box">
                  <p><strong>{{description}}</strong></p>
                  <p>
                    Please review and process this
                    <strong>{{type}}</strong> as soon as possible to ensure
                    timely service delivery.
                  </p>
                </div>

                <!-- Dynamic Data Table -->
                <table
                  cellpadding="0"
                  cellspacing="0"
                  width="100%"
                  style="border-collapse: collapse; margin-bottom: 20px;"
                >
                  {{#if package}}
                  <tr>
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">
                      Package Name:
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                      {{package}}
                    </td>
                  </tr>
                  {{/if}}

                  {{#if refundReason}}
                  <tr>
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">
                      Refund Reason:
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                      {{refundReason}}
                    </td>
                  </tr>
                  {{/if}}

                  {{#if discountCode}}
                  <tr>
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">
                      Discount Code:
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                      {{discountCode}}
                    </td>
                  </tr>
                  {{/if}}

                  {{#if customerName}}
                  <tr>
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">
                      Customer Name:
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                      {{customerName}}
                    </td>
                  </tr>
                  {{/if}}

                  {{#if customerEmail}}
                  <tr>
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">
                      Customer Email:
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                      {{customerEmail}}
                    </td>
                  </tr>
                  {{/if}}

                  {{#if bookingRef}}
                  <tr>
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">
                      Booking Reference:
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                      {{bookingRef}}
                    </td>
                  </tr>
                  {{/if}}

                  {{#if amount}}
                  <tr>
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">
                      Amount:
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                      {{amount}}
                    </td>
                  </tr>
                  {{/if}}

                  {{#if date}}
                  <tr>
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">
                      Date:
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                      {{date}}
                    </td>
                  </tr>
                  {{/if}}
                </table>

                <div class="info-box">
                  <p>
                    <strong>⚠️ Time-Sensitive:</strong> This notification
                    requires prompt attention to maintain our service
                    standards.
                  </p>
                  <p>Please prioritize this task.</p>
                </div>

                <p>
                  Thank you for your attention,<br />
                  <strong>Cedarcrest Hospitals Innovations</strong>
                </p>
              </td>
            </tr>

            <!-- Footer -->
            <tr>
              <td
                style="padding: 20px; text-align: center; background-color: #f5f5f5; color: #777; font-size: 12px;"
              >
                <p style="margin: 0;">
                  This is an automated notification. Please do not reply to this
                  email.
                </p>
                <p style="margin: 0;">
                  &copy; 2025 Cedarcrest Hospitals Innovations. All rights
                  reserved.
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
