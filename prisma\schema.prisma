// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["fullTextSearchPostgres"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}



model User {
  id                            String                        @id @default(uuid()) @map("user_id")
  createdAt                     DateTime                      @default(now()) @map("created_at")
  uhid                          String?                       @unique
  title                         String?
  emailAddress                  String                        @unique @map("email_address")
  gender                        String?
  phoneNumber                   String?                       @map("phone_number")
  dateOfBirth                   DateTime?                     @map("date_of_birth")
  firstName                     String?                       @map("first_name")
  lastName                      String?                       @map("last_name")
  residentialAddress            String?                       @map("residential_address")
  nextOfKin                     String?                       @map("next_of_kin")
  nextOfKinPhone                String?                       @map("next_of_kin_phone")
  transactions                  Transaction[]
  bookings                      PackageBooking[]
  discountRecords               DiscountRecord[]
  referrals                     ExternalReferral[]
  discountAndRefunds            DiscountAndRefund[]
  account                       Account?
}

model PackageCategory {
  id                            Int                           @id @default(autoincrement()) @map("package_category_id")
  createdAt                     DateTime                      @default(now()) @map("created_at")
  name                          String                        @unique
  slug                          String?
  createdBy                     String                        @map("created_by")
  isActive                      Boolean                       @default(true) @map("is_active")
  packages                      Package[]
  updatedBy                     String?                       @map("updated_by")
  updatedAt                     DateTime?                     @updatedAt @map("updated_at")

  @@index([slug])
  @@map("package_category")
}

 model Package {
  id                            Int                           @id @default(autoincrement()) @map("package_id")
  createdAt                     DateTime                      @default(now()) @map("created_at")
  name                          String
  createdBy                     String                        @map("created_by")
  description                   String?
  basePrice                     Decimal                       @default(0.0) @db.Decimal(10, 2) @map("base_price")
  packageStatus                 Boolean                       @default(false) @map("package_status")
  bonusApplicable               Boolean                       @default(false) @map("bonus_applicable")
  includeHospitalReg            Boolean                       @default(false) @map("include_hospital_reg")
  totalSlot                     Int                           @default(0) @map("total_slot")
  slug                          String                        @unique
  packageImage                  String                        @map("package_image")
  categoryId                    Int                           @map("package_category_id")
  packageBookings               PackageBooking[]
  packageLocationPrices         PackageLocationPrice[]
  tests                         Test[]
  updatedBy                     String?                       @map("updated_by")
  category                      PackageCategory               @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@index([slug])
}

 model Update {
  id                            Int                           @id @default(autoincrement()) @map("update_id")
  createdAt                     DateTime                      @default(now()) @map("created_at")
  type                          UpdateType
  itemUpdated                   String                        @map("item_updated")
  identifier                    String
  description                   String?
}

model Region {
  id        Int        @id @default(autoincrement())
  name      String     @unique
  locations Location[]
}

 model Location {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  name                          String
  address                       String?
  country                       String?
  currency                      String                        @default("NGN")
  latitude                      Float?
  longitude                     Float?
  isActive                      Boolean                       @default(true) @map("is_active")
  regionId                      Int?                          @map("region_id")
  region                        Region?                       @relation(fields: [regionId], references: [id])
  packageLocationPrices         PackageLocationPrice[]
  staffs                        Staff[]
  departments                   Department[]
  units                         Unit[]
  cafeteriaMenu                 CafeteriaMenu[]
  cafeteriaInventory            CafeteriaInventory[]
  cafeteriaOrder                CafeteriaOrder[]
  cafeteriaSpecialOrder         CafeteriaSpecialOrder[]
  transactions                  Transaction[]
  referrals                     ExternalReferral[]
  testLocationPrices            TestLocationPrice[]
  rewards                       Reward[]
  DiscountAndRefund             DiscountAndRefund[]
  feedbacks                     Feedback[]


 }

  model Test {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  name                          String                        @unique
  amount                        Decimal                       @default(0.0) @db.Decimal(10, 2)
  packages                      Package[]
  locationPrices                TestLocationPrice[]
 }

  model TestLocationPrice {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  amount                        Decimal                       @default(0.0) @db.Decimal(10, 2)
  locationId                    Int                           @map("location_id")
  testId                        Int                           @map("test_id")
  test                          Test                          @relation(fields: [testId], references: [id])
  location                      Location                      @relation(fields: [locationId], references: [id])

 }

 model PackageLocationPrice {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  amount                        Decimal                       @default(0.0) @db.Decimal(10, 2)
  currency                      String                        @default("NGN")
  startDate                     DateTime?                     @map("start_date")
  endDate                       DateTime?                     @map("end_date")
  deletedAt                     DateTime?                     @map("deleted_at")
  packageId                     Int                           @map("package_id")
  locationId                    Int                           @map("location_id")
  package                       Package                       @relation(fields: [packageId], references: [id])
  location                      Location                      @relation(fields: [locationId], references: [id])
  modifiers                     PackagePriceModifier[]
  bookings                      PackageBooking[]

   @@map("package_location_price")
}

model PackagePriceModifier {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt  @map("updated_at")
  modifierCode                  String                        @unique @map("modifier_code")
  amount                        Decimal                       @default(0.0) @db.Decimal(10, 2)
  startDate                     DateTime?                     @map("start_date")
  endDate                       DateTime?                     @map("end_date")
  isActive                      Boolean                       @default(true) @map("is_active")
  percentage                    Float?
  description                   String?
  packageLocationPrice          PackageLocationPrice[]

   @@map("package_price_modifier")
}

model DiscountRecord {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  userId                        String                        @map("user_id")
  locationId                    Int?
  code                          String
  package                       String
  discountAmount                Decimal                       @default(0.0) @db.Decimal(10, 2) @map("discount_amount")
  user                          User                          @relation(fields: [userId], references: [id])

   @@map("discount_record")
}

model DiscountAndRefund {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt  @map("updated_at")
  userId                        String                        @map("user_id")
  bankName                      String?                       @map("bank_name")
  accountName                   String?                       @map("account_name")
  accountNumber                 String?                       @map("account_number")
  reference                     String
  type                          DiscountAndRefundType
  status                        DiscountAndRefundStatus       @default(PENDING)
  locationId                    Int?                          @map("location_id")
  motivation                    String
  requestedAmount               Decimal?                      @default(0.0) @db.Decimal(10, 2) @map("requested_amount")
  approvedAmount                Decimal?                      @default(0.0) @db.Decimal(10, 2) @map("approved_amount")
  approvedBy                    String?                       @map("approved_by")
  refundPaid                    Boolean                       @default(false) @map("refund_paid")
  discountApplied               Boolean                       @default(false) @map("discount_applied")
  remarks                       String?
  totalBill                     Decimal?                      @default(0.0) @map("total_amount")
  user                          User                          @relation(fields: [userId], references: [id])
  location                      Location?                     @relation(fields: [locationId], references: [id])

   @@map("discount_and_refund")
}

model PackageBooking {
  id                            String                        @id @default(uuid()) @map("package_booking_id")
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime?                     @map("updated_at")
  userId                        String                        @map("user_id")
  amount                        Decimal                       @default(0.0) @db.Decimal(10, 2)
  location                      String?
  totalAmount                   Decimal?                      @default(0.0) @db.Decimal(10, 2) @map("total_amount")
  packageId                     Int                           @map("package_id")
  packageLocationId             Int                           @map("package_location_id")
  referralCode                  String?                       @map("referral_code")
  bookingStatus                 PackageBookingStatus          @default(DRAFT) @map("package_booking_status")
  bookingRef                    String
  voucherCode                   String?                       @map("voucher_code")
  voucherDiscount               Decimal?                      @default(0.0) @db.Decimal(10, 2) @map("voucher_discount")
  updatedBy                     String?                       @map("updated_by")
  packages                      Package                       @relation(fields: [packageId], references: [id])
  packageLocation               PackageLocationPrice          @relation(fields: [packageLocationId], references: [id])
  user                          User                          @relation(fields: [userId], references: [id])

   @@map("package_booking")
}

model Account {
  id                            String                        @id @default(cuid())
  type                          RoleType
  userId                        String?                       @unique @map("user_id")
  staffId                       Int?                          @unique @map("staff_id")
  systemId                      String?                       @unique @map("system_id")

  user                          User?                         @relation(fields: [userId], references: [id])
  staff                         Staff?                        @relation(fields: [staffId], references: [id])
  system                        SystemAccount?                @relation(fields: [systemId], references: [id])

  fromTransactions              Transaction[]                 @relation("FromAccount")
  toTransactions                Transaction[]                 @relation("ToAccount")

}

model Transaction {
  id                            String                        @id @default(cuid()) @map("transaction_id")
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  amount                        Decimal                       @default(0.0) @db.Decimal(10, 2)
  charges                       Decimal?                      @default(0.0) @db.Decimal(10, 2)
  balance                       Decimal                       @default(0.0) @db.Decimal(10, 2)
  reference                     String                        @map("transaction_reference")
  status                        TransactionStatus             @default(PENDING) @map("transaction_status")
  mode                          String                        @map("transaction_mode")
  remarks                       String?
  type                          TransactionType               @map("transaction_type")
  creditPaid                    Boolean?                      @default(false) @map("credit_paid")
  creditPaymentMethod           PaymentType?                  @map("credit_payment_method")
  role                          RoleType?

  userId                        String?                       @map("user_id")
  user                          User?                         @relation(fields: [userId], references: [id])
  staffId                       Int?                          @map("staff_id")
  staff                         Staff?                        @relation(fields: [staffId], references: [id])
  locationId                    Int?                          @map("location_id")
  location                      Location?                     @relation(fields: [locationId], references: [id])

  fromAccountId                 String?
  toAccountId                   String?

  fromAccount                   Account?                      @relation("FromAccount", fields: [fromAccountId], references: [id])
  toAccount                     Account?                      @relation("ToAccount", fields: [toAccountId], references: [id])

  rewardEventLogs               RewardEventLog[]
}

model SystemAccount {
  id                            String                        @id @default(cuid())
  name                          String
  code                          String                        @unique 
  type                          SystemAccountType
  account                       Account?
}

model Staff {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  password                      String?
  locationId                    Int
  location                      Location                      @relation(fields: [locationId], references: [id])
  role                          String?
  email                         String                        @unique
  phoneNumber                   String                        @map("phone_number")
  staffCode                     String?                       @unique @map("staff_code")
  type                          StaffType                     @default(FULL)
  fullName                      String                        @map("full_name")
  isActive                      Boolean                       @default(true) @map("is_active")
  locked                        Boolean                       @default(false)
  lastLogin                     DateTime?                     @map("last_login")
  wallet                        Decimal                       @default(0.0) @db.Decimal(10, 2)
  total                         Decimal                       @default(0.0) @db.Decimal(10, 2)
  creditLimit                   Decimal                       @default(0.0) @db.Decimal(10, 2) @map("credit_limit")
  monthlyCreditUsed             Decimal?                      @default(0.0) @db.Decimal(10, 2) @map("monthly_credit_used")
  mealVoucher                   Decimal                       @default(0.0) @db.Decimal(10, 2) @map("meal_voucher")
  pointsAccrued                 Int?                           @default(0) @map("points_accrued")
  transactions                  Transaction[]
  referralCode                  ReferralCode?
  department                    Department                    @relation(fields: [departmentId], references: [id])
  departmentId                  Int

  unit                          Unit?                         @relation(fields: [unitId], references: [id], onDelete: SetNull)
  unitId                        Int?

  managesDepartment             Department?                   @relation("DepartmentManager")
  headsUnit                     Unit?                         @relation("UnitHead")  

  doctorProfile                 DoctorProfile?                @relation("StaffToDoctorProfile")
  externalReferrals             ExternalReferral[]            @relation("StaffToReferrals")
  cafeteriaOrders               CafeteriaOrder[]
  specialCafeteriaOrders        CafeteriaSpecialOrder[]
  account                       Account?

  roles                         Role[]

  // Innovation Hub
  innovationIdeas               InnovationIdea[]
  innovationIdeaLikes           InnovationIdeaLike[]
  innovationIdeaComments        InnovationIdeaComment[]
  rewardLogs                    RewardEventLog[]

  // Notification relations
  notifications                 Notification[]
  notificationPreference        NotificationPreference?

  // Reward relations
  eligibleRewards               Reward[]                      @relation("StaffEligibleRewards")
  blacklist                     Blacklist[] 
  suggestions                   SuggestionBox[]                 
}

model DoctorProfile {
  staffId                       Int                           @id
  specialtyId                   Int?
  isDoctor                      Boolean                       @default(false) @map("is_doctor")
  isConsultant                  Boolean                       @default(false) @map("is_consultant")
  isVisitingConsultant          Boolean                       @default(false) @map("is_visiting_consultant")
  privilegingDocumentUrl        String?                       @map("privileging_document_url")
  privilegingDocumentStatus     PrivilegingDocumentStatus?    @default(NOT_SUBMITTED) @map("privileging_document_status")
  privilegingDocumentRemarks    String?                       @map("privileging_document_remarks")
  privileged                    Boolean                       @default(false)
  staff                         Staff                         @relation("StaffToDoctorProfile", fields: [staffId], references: [id])
  specialty                     Specialty?                    @relation(fields: [specialtyId], references: [id])
}

model Specialty {
  id                            Int                           @id @default(autoincrement())
  name                          String                        @unique
  doctors                       DoctorProfile[] 
}

model Role {
  id                            Int                           @id @default(autoincrement())
  name                          String                        @unique
  description                   String?
  createdAt                     DateTime                      @default(now())
  updatedAt                     DateTime?                     @updatedAt 

  staffs                        Staff[]
  permissions                   Permission[]
}

model Permission {
  id                            Int                           @id @default(autoincrement())
  action                        String?                       @unique  
  description                   String?
  roles                         Role[]
}

model Department {
  id                            Int                           @id @default(autoincrement())
  name                          String                        @unique
  manager                       Staff?                        @relation("DepartmentManager", fields: [managerId], references: [id])
  managerId                     Int?                          @unique 
  locationId                    Int?                          @map("location_id")
  location                      Location?                     @relation(fields: [locationId], references: [id])
  units                         Unit[]
  staff                         Staff[]
}

model Unit {
  id                            Int                           @id @default(autoincrement())
  name                          String                        @unique
  department                    Department                    @relation(fields: [departmentId], references: [id])
  departmentId                  Int
  head                          Staff?                        @relation("UnitHead", fields: [headId], references: [id])
  headId                        Int?                          @unique 
  locationId                    Int?                          @map("location_id")
  location                      Location?                     @relation(fields: [locationId], references: [id])
  staff                         Staff[]
}

model ReferralCode {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  code                          String                        @unique
  assignedToStaffId             Int                           @unique @map("assigned_to_staff_id")
  isActive                      Boolean                       @default(true) @map("is_active")
  assignedToStaff               Staff                         @relation(fields: [assignedToStaffId], references: [id])
  referralUsages                ReferralCodeUsage[]

   @@map("referral_code")
}

model ReferralCodeUsage {
  id                            Int                           @id @default(autoincrement())
  referralCodeId                Int                           @map("referral_code_id")
  dateUsed                      DateTime                      @default(now()) @map("date_used")
  purpose                       String
  value                         Decimal                       @default(0.0) @db.Decimal(10, 2)
  referralCode                  ReferralCode                  @relation(fields: [referralCodeId], references: [id])

  @@map("referral_code_usage")
}

model Reward {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt  @map("updated_at")
  name                          String?
  description                   String?
  eventType                     RewardEventTypeNew?           @map("event_type")
  valueType                     RewardValueType?              @map("value_type")
  value                         Float
  isActive                      Boolean                       @default(true)

  maxRewardsPerUser             Int?                          @map("max_rewards_per_user")        
  maxRewardsPerDay              Int?                          @map("max_rewards_per_day")
  validFrom                     DateTime?                     @map("valid_from")
  validUntil                    DateTime?                     @map("valid_until")

  staffRewards                  RewardEventLog[]
  location                      Location[]
  eligibleStaff                 Staff[]                       @relation("StaffEligibleRewards")

}

model RewardEventLog {
  id                            Int                           @id @default(autoincrement())
  staffId                       Int
  rewardId                      Int
  receivedAt                    DateTime                      @default(now()) @map("received_at")
  value                         Decimal
  valueType                     RewardValueType               @map("value_type")
  currency                      String?
  eventType                     RewardEventTypeNew?           @map("event_type")
  eventData                     Json?                         
  transactionId                 String?                       
  entityType                    String?                       @map("entity_type")
  entityId                      Int?                         
  staff                         Staff                         @relation(fields: [staffId], references: [id])
  reward                        Reward                        @relation(fields: [rewardId], references: [id])
  transaction                   Transaction?                  @relation(fields: [transactionId], references: [id])

  @@map("reward_event_logs")

}

model Feedback {
  id                            String                        @id @default(cuid())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt  @map("updated_at")
  read                          Boolean                       @default(false)
  name                          String?
  phone                         String?
  type                          String
  email                         String?
  roomNo                        String?                       @map("room_no")
  imageUrl                      String?                       @map("image_url")
  rating                        Int
  feedback                      String
  locationId                    Int?                          @map("location_id")
  location                      Location?                     @relation(fields: [locationId], references: [id])
}

model SystemSettings {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  key                           String                        @unique
  value                         String                        @db.Text
  description                   String?                       @db.Text
  createdBy                     String?                       @map("created_by")
  updatedBy                     String?                       @map("updated_by")

  @@map("system_settings")
}

model ReferringEntity {
  id                            String                        @id @default(uuid())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  entityType                    ReferringEntityType           @map("entity_type")
  entityCategory                EntityCategory                @default(SECONDARY)@map("entity_category")
  name                          String              
  email                         String                        @unique 
  password                      String?              
  phoneNumber                   String                        @map("phone_number")
  activated                     Boolean                       @default(false)
  isEmailValidated              Boolean                       @default(false) @map("is_email_validated")
  canAcceptReferral             Boolean                       @default(false) @map("can_accept_referral")
  dateEmailValidated            DateTime?                     @map("date_email_validated")
  lastLogin                     DateTime?                     @map("last_login")
  resetPasswordToken            String?                       @map("reset_password_token")
  dateResetPasswordRequest      DateTime?                     @map("date_reset_password_request")
  isActive                      Boolean                       @default(false) @map("is_active") 

  medicalLicenseNumber          String?                       @unique @map("medical_license_number")
  organizationAddress           String?                       @map("organization_address")
  primaryContactName            String?                       @map("primary_contact_name") 
  primaryContactEmail           String?                       @map("primary_contact_email")
  primaryContactPhone           String?                       @map("primary_contact_phone")
  specialty                     String?

  referralsSent                 ExternalReferral[]            @relation("ReferralsSent")
  referralsReceived             ExternalReferral[]            @relation("ReferralsReceived")

  visitingConsultantStatus      VisitingConsultantStatus?     @map("visiting_consultant_status")
  consultantApplicationDate     DateTime?                     @map("consultant_application_date")
  submittedCredentials          CredentialDocument[]          @relation("EntityCredentials")
  locationId                    Int?                          @map("location_id")

  @@map("referring_entities")
}

model ExternalReferral {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  referralID                    String                        @map("referral_id")
  specialty                     String?
  referralType                  String                        @map("referral_type")
  referredTo                    String?                       @map("referred_to")
  referringStaff                String?                       @map("referring_staff")
  urgency                       String?
  patientBill                   Decimal?                      @db.Decimal(10, 2) @map("patient_bill")
  reasonForReferral             String                        @db.Text @map("reason_for_referral")
  internalDoctorId              Int?                          @map("internal_doctor_id")
  internalDoctor                Staff?                        @relation("StaffToReferrals", fields: [internalDoctorId], references: [id])

  patientId                     String                        @map("patient_id")
  patient                       User                          @relation(fields: [patientId], references: [id])
  
  referringEntityId             String                        @map("referring_entity_id")
  referringEntity               ReferringEntity               @relation("ReferralsSent", fields: [referringEntityId], references: [id])

  receivingEntityId             String                        @map("receiving_entity_id")
  receivingEntity               ReferringEntity               @relation("ReferralsReceived", fields: [receivingEntityId], references: [id])
  
  status                        ReferralStatus                @default(PENDING_REVIEW)
  locationId                    Int?                          @map("location_id")
  location                      Location?                     @relation(fields: [locationId], references: [id])
  comments                      ReferralComment[]

  @@map("external_referrals")
}

model ReferralComment {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  sender                        String
  comment                       String                        @db.Text  
  referralId                    Int
  referral                      ExternalReferral?             @relation(fields: [referralId], references: [id])

  @@map("referral_comments")
}

model CredentialDocument {
  id                            String                        @id @default(uuid())
  referringEntityId             String                        @map("referring_entity_id")
  referringEntity               ReferringEntity               @relation("EntityCredentials", fields: [referringEntityId], references: [id])

  documentType                  CredentialDocumentType        @map("document_type")
  fileName                      String                        @map("file_name")
  fileUrl                       String                        @map("file_url") 
  uploadedAt                    DateTime                      @default(now()) @map("uploaded_at")

  verificationStatus            CredentialVerificationStatus  @default(PENDING) @map("verification_status")
  verifiedAt                    DateTime?                     @map("verified_at")
  verifierNotes                 String?                       @db.Text @map("verifier_notes")

  @@map("credential_documents")
}

model CafeteriaMenu {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  name                          String
  generalPrice                  Decimal                       @db.Decimal(10, 2) @map("general_price")
  staffPrice                    Decimal?                      @db.Decimal(10, 2) @map("staff_price")
  specialPrice                  Decimal?                      @db.Decimal(10, 2) @map("special_price")
  isAvailable                   Boolean                       @default(true) @map("is_available")
  createdBy                     String                        @map("created_by")
  menuCategoryId                Int                           @map("menu_category_id")
  locationId                    Int                           @map("location_id")
  location                      Location                      @relation(fields: [locationId], references: [id])
  menuCategory                  CafeteriaMenuCategory         @relation(fields: [menuCategoryId], references: [id])
  orderItems                    CafeteriaOrderItem[]
  returnedItems                 CafeteriaReturnedItem[]

  @@map("cafeteria_menu")
}

model CafeteriaMenuCategory {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  name                          String
  createdBy                     String                        @map("created_by")
  description                   String?
  menu                          CafeteriaMenu[]           
  isAvailable                   Boolean                       @default(true) @map("is_available")

  @@map("cafeteria_menu_category")
}

model CafeteriaInventoryCat {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  createdBy                     String?                       @map("created_by")
  name                          String                    
  inventories                   CafeteriaInventory[]

  @@map("cafeteria_inventory_category")
}


model CafeteriaInventory {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  itemName                      String                        @map("item_name")
  createdBy                     String                        @map("created_by")
  inventoryCategoryId           Int
  inventoryCat                  CafeteriaInventoryCat         @relation(fields: [inventoryCategoryId], references: [id])
  currentStock                  Decimal                       @db.Decimal(10, 2)  @map("current_stock")
  minimumStock                  Decimal                       @db.Decimal(10, 2)  @map("minimum_stock")
  unit                          String
  locationId                    Int                           @map("location_id")
  location                      Location                      @relation(fields: [locationId], references: [id])
  supplies                      CafeteriaSupply[]
  StockIssue                    CafeteriaStockIssue[]

  @@map("cafeteria_inventory")
}

model CafeteriaStockIssue {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  inventoryItemId               Int                           @map("inventory_item_id")
  quantity                      Decimal                       @db.Decimal(10, 2) 
  comment                       String
  issuedBy                      String                        @map("issued_by")
  inventoryItem                 CafeteriaInventory            @relation(fields: [inventoryItemId], references: [id])

  @@map("cafeteria_stock_issue")
}

model CafeteriaSupply {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  inventoryItemId               Int                           @map("inventory_item_id")
  supplier                      String
  quantitySupplied              Decimal                       @db.Decimal(10, 2) @map("quantity_supplied")
  costPerUnit                   Decimal                       @db.Decimal(10, 2) @map("cost_per_unit")
  totalCost                     Decimal                       @db.Decimal(10, 2) @map("total_cost")
  invoiceNo                     String?                       @map("invoice_no")
  createdBy                     String                        @map("created_by")
  locationId                    Int?                          @map("location_id")
  invoice                       String?
  inventoryItem                 CafeteriaInventory            @relation(fields: [inventoryItemId], references: [id])

  @@map("cafeteria_supplies")
}

model CafeteriaOrder {
  id                            String                        @id @default(cuid())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  orderNumber                   String                        @unique @map("order_number")
  staffId                       Int?                          @map("staff_id")
  customerName                  String?                       @map("customer_name")
  patientRoomNo                 String?                       @map("patient_room_no")
  customerPhone                 String?                       @map("customer_phone")
  totalAmount                   Decimal                       @db.Decimal(10, 2) @map("total_amount")
  paymentType                   PaymentType                   @map("payment_type")
  orderType                     OrderType                     @map("order_type")
  creditPaid                    Boolean?                      @default(false)@map("credit_paid") 
  creditPaymentMethod           String?                       @map("credit_payment_method")      
  servedBy                      String?                       @map("served_by")
  saleType                      SaleType                      @default(STAFF) @map("sale_type")
  specialInstructions           String?                       @map("special_instructions")
  locationId                    Int                           @map("location_id")
  location                      Location                      @relation(fields: [locationId], references: [id])
  staff                         Staff?                        @relation(fields: [staffId], references: [id])
  orderItems                    CafeteriaOrderItem[]

  @@map("cafeteria_orders")
}

model CafeteriaSpecialOrder {
  id                            String                        @id @default(cuid())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")
  orderNumber                   String                        @unique @map("order_number")
  totalAmount                   Decimal                       @db.Decimal(10, 2) @map("total_amount")
  receivedTotalAmount           Decimal?                      @db.Decimal(10, 2) @map("received_total_amount")
  returnedTotalAmount           Decimal?                      @db.Decimal(10, 2) @map("returned_total_amount")
  status                        SpecialOrderStatus            @default(PENDING) @map("status")
  purpose                       String                       
  comment                       String?                       
  approvedBy                    String?                       @map("approved_by")
  declinedBy                    String?                       @map("declined_by")
  deliveredBy                   String?                       @map("delivered_by")
  receivedBy                    String?                       @map("received_by")
  receivedAt                    DateTime?                     @map("received_at")
  neededBy                      DateTime                      @map("needed_by")
  locationId                    Int                           @map("location_id")
  staffId                       Int                           @map("staff_id")
  location                      Location                      @relation(fields: [locationId], references: [id])
  staff                         Staff                         @relation(fields: [staffId], references: [id])
  orderItems                    CafeteriaOrderItem[]
  returnedItems                 CafeteriaReturnedItem[]

  @@map("cafeteria_special_orders")
}

model CafeteriaOrderItem {
  id                            Int                           @id @default(autoincrement())
  orderId                       String?                        @map("order_id")
  specialOrderId                String?                       @map("special_order_id")
  menuItemId                    Int                           @map("menu_item_id")
  quantity                      Int
  receivedQuantity              Int?                          @map("received_quantity")
  unitPrice                     Decimal                       @db.Decimal(10, 2) @map("unit_price")
  totalPrice                    Decimal                       @db.Decimal(10, 2) @map("total_price")
  specialRequests               String?                       @map("special_requests")
  order                         CafeteriaOrder?               @relation(fields: [orderId], references: [id], onDelete: Cascade)
  specialOrder                  CafeteriaSpecialOrder?        @relation(fields: [specialOrderId], references: [id], onDelete: Cascade)
  menuItem                      CafeteriaMenu                 @relation(fields: [menuItemId], references: [id])

  @@map("cafeteria_order_items")
}

model CafeteriaReturnedItem {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at")
  specialOrderId                String                        @map("special_order_id")
  menuItemId                    Int                           @map("menu_item_id")
  returnedQuantity              Int                           @map("returned_quantity")
  reason                        String?
  returnedBy                    String                        @map("returned_by")
  specialOrder                  CafeteriaSpecialOrder         @relation(fields: [specialOrderId], references: [id], onDelete: Cascade)
  menuItem                      CafeteriaMenu                 @relation(fields: [menuItemId], references: [id])

  @@map("cafeteria_returned_items")
}

// Notification models
model Notification {
  id                            String                        @id @default(cuid())
  staffId                       Int                           @map("staff_id")
  type                          String
  title                         String
  message                       String                        @db.Text
  data                          String?                       @db.Text // JSON string for additional data
  priority                      String                        @default("low") // low, medium, high
  isRead                        Boolean                       @default(false) @map("is_read")
  readAt                        DateTime?                     @map("read_at")
  createdAt                     DateTime                      @default(now()) @map("created_at")
  updatedAt                     DateTime                      @updatedAt @map("updated_at")

  staff                         Staff                         @relation(fields: [staffId], references: [id], onDelete: Cascade)

  @@index([staffId, isRead])
  @@index([staffId, createdAt])
  @@map("notifications")
}

model NotificationPreference {
  id                            String                        @id @default(cuid())
  staffId                       Int                           @unique @map("staff_id")
  emailNotifications            Boolean                       @default(true) @map("email_notifications")
  pushNotifications             Boolean                       @default(true) @map("push_notifications")
  forumNotifications            Boolean                       @default(true) @map("forum_notifications")
  gameNotifications             Boolean                       @default(true) @map("game_notifications")
  mentionNotifications          Boolean                       @default(true) @map("mention_notifications")
  createdAt                     DateTime                    @default(now()) @map("created_at")
  updatedAt                     DateTime                    @updatedAt @map("updated_at")

  staff                Staff   @relation(fields: [staffId], references: [id], onDelete: Cascade)

  @@map("notification_preferences")
}


model InnovationCategory {
  id                            Int                           @id @default(autoincrement())
  name                          String                        @unique
  ideas                         InnovationIdea[]
  createdAt                     DateTime                      @default(now())
}

model InnovationIdea {
  id                            Int                           @id @default(autoincrement())
  createdAt                     DateTime                      @default(now()) @map("created_at") 
  reviewedAt                    DateTime?                     @map("review_at")   
  acceptedAt                    DateTime?                     @map("accepted_at")
  rejectedAt                    DateTime?                     @map("rejected_at")
  implementedAt                 DateTime?                     @map("implemented_at")                      
  title                         String
  slug                          String?
  description                   String
  deleted                       Boolean                       @default(false)
  status                        InnovationIdeaStatus          @default(DRAFT)
  author                        Staff                         @relation( fields: [authorId], references: [id])
  authorId                      Int
  category                      InnovationCategory?           @relation( fields: [categoryId], references: [id])
  categoryId                    Int?
  likes                         InnovationIdeaLike[]
  comments                      InnovationIdeaComment[]
  tags                          InnovationTag[]
}

model InnovationTag {
  id                            Int                           @id @default(autoincrement())
  name                          String                        @unique
  ideas                         InnovationIdea[]             
  createdAt                     DateTime                      @default(now())
}

model InnovationIdeaLike {
  id                            Int                           @id @default(autoincrement())
  staff                         Staff                         @relation(fields: [staffId], references: [id])
  staffId                       Int                           
  idea                          InnovationIdea                @relation(fields: [ideaId], references: [id], onDelete: Cascade)
  ideaId                        Int
  liked                         Boolean                       @default(true)
  createdAt                     DateTime                      @default(now())

  @@unique([staffId, ideaId]) 
}

model InnovationIdeaComment {
  id                            Int                           @id @default(autoincrement())
  content                       String
  staff                         Staff                         @relation(fields: [staffId], references: [id])
  staffId                       Int
  idea                          InnovationIdea                @relation(fields: [ideaId], references: [id], onDelete: Cascade)
  ideaId                        Int
  createdAt                     DateTime                      @default(now())
}

model Blacklist {
  id                            Int                           @id @default(autoincrement())
  staff                         Staff                         @relation(fields: [staffId], references: [id])
  staffId                       Int
  action                        RewardEventTypeNew?
  reason                        String?
  blockedAt                     DateTime                      @default(now()) @map("blocked_at")
  unblockedAt                   DateTime?                     @map("unblocked_at")
  blockedBy                     String?                       @map("blocked_by")
  unblockedBy                   String?                       @map("unblocked_by")
  isActive                      Boolean                       @default(true)
}

model SuggestionBox {
  id                            Int                           @id @default(autoincrement())
  staff                         Staff                         @relation(fields: [staffId], references: [id])
  staffId                       Int
  content                       String
  createdAt                     DateTime                      @default(now()) @map("created_at")
  isRead                        Boolean                       @default(false)
  anonymous                     Boolean                       @default(false)
  comment                       String?
}

enum RewardValueType {
  PERCENTAGE
  POINTS
  CASH
  VOUCHER
  BADGE
  CERTIFICATE
}

enum RewardEventType {
  INNOVATION_IDEA_POSTED
  INNOVATION_IDEA_COMMENT
  INNOVATION_IDEA_LIKE
  PACKAGE_CONFIRMED
  PACKAGE_REFERRAL
  GAME_COMPLETION
  CUSTOM_EVENT
  ACHIEVEMENT_UNLOCKED
  MILESTONE_REACHED
}

enum RewardEventTypeNew { 
  INNOVATION_IDEA_POSTED 
  INNOVATION_IDEA_COMMENT 
  INNOVATION_IDEA_LIKE 
  INNOVATION_IDEA_ACCEPTED 
  PACKAGE_REFERRAL_ONSITE 
  PACKAGE_REFERRAL_ONLINE 
  }

enum PackageBookingStatus {
  DRAFT
  PENDING
  COMPLETED
}

enum SystemAccountType {
  COMPANY
  REWARDS
  CAFETERIA
}

enum FeedbackType {
  INPATIENT
  OUTPATIENT
}

enum TransactionStatus {
  PENDING
  SUCCESS
  CANCELLED 
  PROCESSING
  FAILED
  REVERSED      
  CONFIRMED
}

enum PrivilegingDocumentStatus {
  NOT_SUBMITTED
  SUBMITTED
  UNDER_REVIEW
  APPROVED
  REJECTED
}

enum TransactionType {
  WITHDRAWAL
  TRANSFER
  PURCHASE  
  REFUND   
  REWARD  
  FEE      
  PACKAGE
  CAFETERIA                                                
  INBOUND
  OUTBOUND
}

enum StaffType {
  FULL
  CONTRACT
  INTERN
}

enum RoleType {
  USER
  STAFF
  SYSTEM
  ADMIN
}

enum ReferralStatus {
  PENDING_REVIEW
  ACCEPTED
  IN_PROGRESS
  COMPLETED   
  REJECTED
  CANCELLED
}

enum ReferringEntityType {
  INDIVIDUAL_DOCTOR
  ORGANIZATION
}

enum EntityCategory {
  PRIMARY
  SECONDARY
}

enum VisitingConsultantStatus {
  NOT_APPLICABLE     
  APPLICATION_STARTED 
  PENDING_APPROVAL   
  APPROVED           
  REJECTED          
  ONBOARDED     
}

enum CredentialDocumentType {
  MEDICAL_LICENSE
  CV_RESUME
  CERTIFICATION
  IDENTIFICATION 
  OTHER
}

enum CredentialVerificationStatus {
  PENDING
  VERIFIED
  REJECTED
  INFORMATION_REQUESTED 
}

enum PaymentType {
  TRANSFER
  CARD
  WALLET
  CREDIT
  VOUCHER
  SPECIAL
  PATIENT
}

enum OrderType {
  DINE_IN
  TAKEAWAY
  DELIVERY
}

enum SaleType {
  STAFF
  GENERAL
  SPECIAL
  PATIENT
}

enum SpecialOrderStatus {
  PENDING
  APPROVED
  DECLINED
  DELIVERED
  RECEIVED
}

enum UpdateType {
  PATIENT_REFERRAL
  PACKAGE_BOOKING
  REFERRING_ENTITY
  REWARD
  PACKAGE
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  DECLINED
  EXPIRED
}

enum InnovationIdeaStatus {
  DRAFT
  PENDING_REVIEW
  ACCEPTED
  REJECTED
  IMPLEMENTED
}

enum DiscountAndRefundType {
  DISCOUNT
  REFUND
}

enum DiscountAndRefundStatus {
  PENDING
  APPROVED
  REJECTED
}