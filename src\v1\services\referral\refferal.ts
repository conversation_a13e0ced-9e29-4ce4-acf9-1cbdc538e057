import * as bcrypt from 'bcryptjs';
import { db } from '../../utils/model';
import { generateCode, timestamp, formatDate } from '../../utils/util';
import { logger } from '../../utils/logger';
import { createDateFilter } from '../../utils/util';
import config from '../../../config/app.config';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../../utils/cache';
import { devLog } from '../../utils/logger';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import { HttpError } from '../../utils/httpError';
import { userService } from '../booking/user';
import { broadcast, sendToUser } from '../socket';

// Helper function to clear all referral-related caches
export const clearReferralCaches = async (): Promise<void> => {
  await deleteCacheByPattern('referral:*');
};

export const referralService = {
  // Create a new referral
  newReferral: async (reqBody: any) => {
    try {
      const {
        specialty,
        referralType,
        referredTo,
        urgency,
        reasonForReferral,
        referringEntityId,
        receivingEntityId,
        commentNeeded,
        ...userData
      } = reqBody;
      const user = await userService.createOrUpdateUser(userData);
      const referralId = `REF-${timestamp()}${generateCode(5)?.toUpperCase()}`;
      const result = await db.externalReferral.create({
        data: {
          referralID: referralId,
          specialty,
          referralType,
          referredTo,
          urgency,
          reasonForReferral,
          referringEntityId,
          receivingEntityId,
          patientId: user.id,
        },
        include: {
          referringEntity: true,
          receivingEntity: true,
        },
      });
      //Send notification emails to admins
      const name = result.receivingEntity.name;
      const email = result.receivingEntity.email;
      const mailOptions = {
        from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
        to: email,
        subject: 'URGENT: New Referral Alert',
        template: 'referral-notification',
        context: {
          name: name,
          url: config.REFERRAL_BASE_URL,
        },
      };
      enqueueSendEmailJob(mailOptions);
      await clearReferralCaches();

      // Emit socket notification for new referral
      broadcast('new_referral_created', {
        referralId: result.id,
        referralID: referralId,
        patientName: `${userData.firstName} ${userData.lastName}`,
        referringEntity: result.referringEntity.name,
        receivingEntity: result.receivingEntity.name,
        urgency,
        timestamp: new Date(),
      });

      return { message: 'Referral sent succesfully' };
    } catch (error) {
      logger.error('Failed to send referral', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to send referral', 400);
    }
  },

  //Update an existing referral
  updateReferral: async (reqBody: any) => {
    try {
      const { accountId, ...updateData } = reqBody;
      const referralId = updateData.id;

      // Fetch the current referral to check permissions
      const existingReferral = await db.externalReferral.findUnique({
        where: { id: Number(referralId) },
      });

      if (!existingReferral) {
        throw new HttpError('Referral not found', 404);
      }

      // Verify that the update is being made by the receiving entity
      if (accountId !== existingReferral.receivingEntityId) {
        throw new HttpError(
          'Only the receiving entity can update this referral',
          403
        );
      }

      // Validate status if it's being updated
      if (updateData.status) {
        const validStatuses = [
          'ACCEPTED',
          'IN_PROGRESS',
          'COMPLETED',
          'REJECTED',
          'CANCELLED',
        ];
        if (!validStatuses.includes(updateData.status)) {
          throw new HttpError(`Invalid status`, 400);
        }
      }

      const updatedData = await db.externalReferral.update({
        where: { id: Number(referralId) },
        data: updateData,
        include: {
          referringEntity: {
            select: {
              name: true,
            },
          },
          receivingEntity: {
            select: {
              name: true,
            },
          },
        },
      });

      await clearReferralCaches();

      // Emit socket notification for referral update
      broadcast('referral_status_updated', {
        referralId: Number(referralId),
        referralID: updatedData.referralID,
        status: updateData.status,
        referringEntity: updatedData.referringEntity?.name,
        receivingEntity: updatedData.receivingEntity?.name,
        timestamp: new Date(),
      });

      return { message: 'Referral updated successfully' };
    } catch (error) {
      logger.error('Failed to update referral', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update referral', 400);
    }
  },

  referralStats: async (accountId: string) => {
    try {
      const whereClause = {
        OR: [
          { referringEntityId: accountId },
          { receivingEntityId: accountId },
        ],
      };

      const [totalReferrals, statusCounts] = await Promise.all([
        db.externalReferral.count({
          where: whereClause,
        }),
        db.externalReferral.groupBy({
          by: ['status'],
          where: whereClause,
          _count: { status: true },
        }),
      ]);

      const formatStatusKey = (status: string) => {
        return status.toLowerCase(); // or use a custom mapping if needed
      };

      const statusSummary = statusCounts.reduce(
        (acc, item) => {
          acc[formatStatusKey(item.status)] = item._count.status;
          return acc;
        },
        {} as Record<string, number>
      );

      return {
        total: totalReferrals,
        ...statusSummary,
      };
    } catch (error) {
      logger.error('Failed to get referral stats', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get referral stats', 400);
    }
  },

  // Get all referrals with pagination, search, and caching
  getReferrals: async (accountId: string, query: any = {}) => {
    try {
      const page: number = parseInt(query.page as string) || 1;
      const limit: number = parseInt(query.limit as string) || 10;
      const search: string = (query.search as string) || '';
      const status = query.status;
      const role = query.role;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const dateFilter = createDateFilter(startDate, endDate);

      let whereClause: any = {
        ...(status ? { status: status.toUpperCase() } : {}),
        ...dateFilter,
      };

      if (role === 'sent') {
        whereClause.referringEntityId = accountId;
      } else if (role === 'received') {
        whereClause.receivingEntityId = accountId;
      } else {
        whereClause.OR = [
          { referringEntityId: accountId },
          { receivingEntityId: accountId },
        ];
      }

      if (search) {
        const searchFilter = {
          OR: [
            { referralID: { contains: search } },
            { referralType: { contains: search } },
            { patient: { emailAddress: { contains: search } } },
          ],
        };

        if (whereClause.OR) {
          const accountFilter = { OR: whereClause.OR };
          delete whereClause.OR;

          whereClause = {
            AND: [accountFilter, searchFilter],
            ...whereClause,
          };
        } else {
          whereClause = {
            ...whereClause,
            ...searchFilter,
          };
        }
      }

      const [referrals, totalCount] = await db.$transaction([
        db.externalReferral.findMany({
          include: {
            referringEntity: {
              select: {
                name: true,
                email: true,
              },
            },
            patient: {
              select: {
                emailAddress: true,
              },
            },
            receivingEntity: {
              select: {
                name: true,
                email: true,
              },
            },
          },
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
        }),
        db.externalReferral.count({
          where: whereClause,
        }),
      ]);

      const response = {
        referrals: referrals,
        totalPages: Math.ceil(totalCount / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
      };

      return response;
    } catch (error) {
      logger.error('Failed to get all referrals', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get all referrals', 400);
    }
  },

  singleReferral: async (accountId: any, referralId: any) => {
    try {
      // Try to get from cache first
      const cacheKey = `referral:single:${referralId}`;
      const cachedReferral = await getCache<any>(cacheKey);

      if (cachedReferral) {
        devLog(`Cache hit for referral ${referralId}`);
        return cachedReferral;
      }

      // If not in cache, fetch from database
      devLog(`Cache miss for referral ${referralId}, fetching from database`);
      const referral = await db.externalReferral.findFirst({
        where: { id: Number(referralId) },
        include: {
          comments: true,
          patient: {
            select: {
              emailAddress: true,
              phoneNumber: true,
              firstName: true,
              lastName: true,
              dateOfBirth: true,
            },
          },
          receivingEntity: {
            select: {
              name: true,
              email: true,
              phoneNumber: true,
              primaryContactEmail: true,
              primaryContactName: true,
              primaryContactPhone: true,
              entityType: true,
            },
          },
        },
      });

      if (!referral) {
        throw new HttpError('referral not found', 400);
      }

      // Store in cache for future requests (cache for 30 minutes)
      // await setCache(cacheKey, referral, 1800);

      return referral;
    } catch (error) {
      logger.error('Failed to get single referral', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get single referral', 400);
    }
  },

  //Add comment to referral
  postComment: async (reqBody: any) => {
    try {
      const { sender, comment, referralId } = reqBody;

      const existingReferral = await db.externalReferral.findUnique({
        where: { id: Number(referralId) },
      });

      if (!existingReferral) {
        throw new HttpError('Referral not found', 404);
      }

      const newComment = await db.referralComment.create({
        data: {
          sender,
          comment,
          referralId,
        },
      });

      await deleteCache(`referral:single:${referralId}`);
      await clearReferralCaches();

      // Emit socket notification for new comment
      broadcast('referral_comment_added', {
        referralId: Number(referralId),
        commentId: newComment.id,
        sender,
        comment,
        timestamp: new Date(),
      });

      return { message: 'Referral updated successfully' };
    } catch (error) {
      logger.error('Failed to post comment', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to post comment', 400);
    }
  },
};
