import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { createDateFilter } from '../../utils/util';
import { logger } from '../../utils/logger';

export const SuggestionBoxService = {
  getAllSuggestions: async (staffId: number, query: any) => {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        startDate,
        endDate,
        singleDate,
        isRead,
      } = query;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);
      const dateFilter = createDateFilter(
        startDate,
        endDate,
        'createdAt',
        singleDate
      );

      const canSeeAll = await staffHasPermission(
        staffId,
        PERMISSIONS.STAFF_EDIT
      ).catch(() => false);

      const whereClause: any = {
        ...(search
          ? {
              OR: [{ content: { search: search } }],
            }
          : {}),
        ...dateFilter,
        ...(isRead !== undefined
          ? { isRead: isRead === 'true' || isRead === true }
          : {}),
        ...(!canSeeAll && { staffId }),
      };

      const [suggestions, total] = await Promise.all([
        db.suggestionBox.findMany({
          where: whereClause,
          skip,
          take,
          orderBy: { createdAt: 'desc' },
          include: {
            staff: {
              select: {
                fullName: true,
                email: true,
              },
            },
          },
        }),
        db.suggestionBox.count({ where: whereClause }),
      ]);

      return {
        suggestions,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / take),
          totalItems: total,
          itemsPerPage: take,
        },
      };
    } catch (error) {
      logger.error('Error getting suggestions:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch suggestions', 400);
    }
  },

  createSuggestion: async (staffId: number, reqBody: any) => {
    try {
      const { content, anonymous = false } = reqBody;

      if (!content || content.trim().length === 0) {
        throw new HttpError('Suggestion content is required', 400);
      }

      const suggestion = await db.suggestionBox.create({
        data: {
          staffId,
          content: content.trim(),
          anonymous,
        },
      });

      return { message: 'Suggestion created successfully' };
    } catch (error) {
      logger.error('Error creating suggestion:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create suggestion', 400);
    }
  },

  updateSuggestion: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.HUB_EDIT);
      const { suggestionId, comment } = reqBody;

      // Check if suggestion exists
      const existingSuggestion = await db.suggestionBox.findUnique({
        where: { id: suggestionId },
      });

      if (!existingSuggestion) {
        throw new HttpError('Suggestion not found', 404);
      }

      // Prepare update data
      const updateData: any = {};
      updateData.isRead = true;
      if (comment !== undefined) {
        updateData.comment = comment;
      }

      await db.suggestionBox.update({
        where: { id: Number(suggestionId) },
        data: updateData,
      });

      return { message: 'Suggestion updated successfully' };
    } catch (error) {
      logger.error('Error updating suggestion:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update suggestion', 400);
    }
  },

  deleteSuggestion: async (staffId: number, suggestionId: number) => {
    try {
      // Check if suggestion exists
      const existingSuggestion = await db.suggestionBox.findUnique({
        where: { id: suggestionId },
      });

      if (!existingSuggestion) {
        throw new HttpError('Suggestion not found', 404);
      }

      // Check if the staff member owns the suggestion or has admin permissions
      let hasPermission = false;
      try {
        hasPermission = await staffHasPermission(
          staffId,
          PERMISSIONS.FEEDBACK_VIEW
        ); // Using feedback permission as proxy for admin
      } catch (error) {
        // If permission check fails, user doesn't have admin permissions
        hasPermission = false;
      }

      if (existingSuggestion.staffId !== staffId && !hasPermission) {
        throw new HttpError(
          'You do not have permission to delete this suggestion',
          403
        );
      }

      await db.suggestionBox.delete({
        where: { id: suggestionId },
      });

      return { message: 'Suggestion deleted successfully' };
    } catch (error) {
      logger.error('Error deleting suggestion:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete suggestion', 400);
    }
  },
};
