import { db, decimal } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';
import {
  transactionStatus,
  transactionType,
  createDateFilter,
  createCommonDateFilter,
} from '../../utils/util';
import * as bcrypt from 'bcryptjs';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import dayjs from 'dayjs';
import { getMainSystemAccount } from '../system';
import { sendToUser, broadcast } from '../socket';
import { notificationService } from '../notification';
import { logger } from '../../utils/logger';
import { createCSVExport, CSV_CONFIGS } from '../../utils/csvExport';
import { timestamp, generateCode } from '../../utils/util';

export const transactionService = {
  getAllTransactions: async (staffId: any, query: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const canManage = await auth.hasPermission(PERMISSIONS.TRANSACTION_EDIT);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (hasLocationAll) {
        // No location filter - get all data
      } else if (hasRegion) {
        const regionId = await auth.getRegionId();
        locationFilter = {
          location: {
            regionId,
          },
        };
      } else {
        const locationId = await auth.getLocationId();
        locationFilter = { locationId };
      }

      // Parse pagination parameters with defaults
      const page: number = parseInt(query.page as string) || 1;
      const limit: number = parseInt(query.limit as string) || 10;

      // Parse filter parameters
      const status = query.status;
      const type = query.type;
      const search = query.search;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const singleDate = query.singleDate as string;
      const exportFormat = query.export as string;
      const presetDate = query.presetDate as string;

      // Validate filter parameters
      if (status && !transactionStatus.includes(status.toUpperCase())) {
        throw new HttpError('Invalid transaction status', 400);
      }
      if (type && !transactionType.includes(type.toUpperCase())) {
        throw new HttpError('Invalid transaction type', 400);
      }

      // Create date filter using the reusable function
      const dateFilter = createDateFilter(
        startDate,
        endDate,
        'createdAt',
        singleDate
      );

      const daysFilter = createCommonDateFilter(presetDate as any);
      // Create stats date filter
      let selectedYear = query?.year;
      let selectedMonth = query?.month;
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      let statsDateFilter = {};
      if (startDate || endDate || singleDate) {
        statsDateFilter = createDateFilter(
          startDate,
          endDate,
          'createdAt',
          singleDate
        );
      } else if (selectedYear || selectedMonth) {
        if (!selectedYear || isNaN(Number(selectedYear))) {
          selectedYear = currentYear;
        } else {
          selectedYear = Number(selectedYear);
        }

        if (
          !selectedMonth ||
          isNaN(Number(selectedMonth)) ||
          Number(selectedMonth) < 1 ||
          Number(selectedMonth) > 12
        ) {
          selectedMonth = currentMonth;
        } else {
          selectedMonth = Number(selectedMonth);
        }

        const monthStart = dayjs(`${selectedYear}-${selectedMonth}-01`)
          .startOf('month')
          .toDate();
        const monthEnd = dayjs(`${selectedYear}-${selectedMonth}-01`)
          .endOf('month')
          .toDate();

        statsDateFilter = {
          createdAt: {
            gte: monthStart,
            lte: monthEnd,
          },
        };
      }
      const todayStart = dayjs().startOf('day').toDate();
      const todayEnd = dayjs().endOf('day').toDate();

  //     const baseWhereClause = {
  //          ...(searchTransaction && {
  //   OR: [
  //     { reference: { contains: searchTransaction } },
  //     { mode: { contains: searchTransaction } },
  //   ],
  // }),
  //       ...(status ? { status: status.toUpperCase() } : {}),
  //       ...(type ? { type: type.toUpperCase() } : {}),
  //       ...locationFilter,
  //     };

      const defaultDailyFilter = {
        createdAt: {
          gte: todayStart,
          lte: todayEnd,
        },
      };

      
      const searchFilter = search
        ? {
            OR: [
              { reference: { contains: search, mode: 'insensitive' } },
 { mode: { contains: search, mode: 'insensitive' } },
            ],
          }
        : {};

      const whereClause = {
               ...(search && {
    OR: [
      { reference: search },
      { mode: { contains: search } },
    ],
  }),
        ...(status ? { status: status.toUpperCase() } : {}),
        ...(type ? { type: type.toUpperCase() } : {}),
        ...locationFilter,
        ...(startDate || endDate || singleDate
          ? dateFilter
          : presetDate
            ? daysFilter
            : query.month || query.year
              ? statsDateFilter
              : defaultDailyFilter),
      };

      // Handle CSV export
      if (exportFormat === 'csv') {
        const transactions = await db.transaction.findMany({
          where: whereClause,
          orderBy: { createdAt: 'asc' },
          include: {
            fromAccount: {
              select: {
                user: {
                  select: {
                    emailAddress: true,
                    phoneNumber: true,
                    firstName: true,
                    uhid: true,
                  },
                },
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            toAccount: {
              select: {
                user: {
                  select: {
                    emailAddress: true,
                    phoneNumber: true,
                    firstName: true,
                    uhid: true,
                  },
                },
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        });

        const flattenedTransactions = transactions.map((transaction: any) => {
          const baseTransaction = {
            reference: transaction.reference,
            amount: Number(transaction.amount),
            status: transaction.status,
            type: transaction.type,
            mode: transaction.mode,
            date: transaction.createdAt.toLocaleString(),
            remarks: transaction.remarks || 'N/A',
            fromAccountType: transaction.fromAccount?.user
              ? 'User'
              : transaction.fromAccount?.staff
                ? 'Staff'
                : transaction.fromAccount?.system
                  ? 'System'
                  : 'N/A',
            fromAccountName:
              transaction.fromAccount?.user?.firstName ||
              transaction.fromAccount?.staff?.fullName ||
              transaction.fromAccount?.system?.name ||
              'N/A',
            toAccountType: transaction.toAccount?.user
              ? 'User'
              : transaction.toAccount?.staff
                ? 'Staff'
                : transaction.toAccount?.system
                  ? 'System'
                  : 'N/A',
            toAccountName:
              transaction.toAccount?.user?.firstName ||
              transaction.toAccount?.staff?.fullName ||
              transaction.toAccount?.system?.name ||
              'N/A',
          };

          // Add creditPaid column only for credit mode transactions
          if (transaction.mode === 'credit') {
            return {
              ...baseTransaction,
              creditPaid: transaction.creditPaid ? 'YES' : 'NO',
            };
          }

          return baseTransaction;
        });

        // Create dynamic headers based on whether any transactions have credit mode
        const hasCreditTransactions = transactions.some(
          (transaction) => transaction.mode === 'credit'
        );
        const baseHeaders = [
          'reference',
          'amount',
          'status',
          'type',
          'mode',
          'date',
          'remarks',
          'fromAccountType',
          'fromAccountName',
          'toAccountType',
          'toAccountName',
        ];

        const headers = hasCreditTransactions
          ? [...baseHeaders.slice(0, 5), 'creditPaid', ...baseHeaders.slice(5)]
          : baseHeaders;

        return createCSVExport(flattenedTransactions, 'transactions', {
          ...CSV_CONFIGS.transactions,
          headers,
        });
      }

      const [transactions, totalPages, totalCount] = await db.$transaction([
        db.transaction.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          include: {
            fromAccount: {
              select: {
                user: {
                  select: {
                    emailAddress: true,
                    phoneNumber: true,
                    firstName: true,
                    uhid: true,
                  },
                },
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            toAccount: {
              select: {
                user: {
                  select: {
                    emailAddress: true,
                    phoneNumber: true,
                    firstName: true,
                    uhid: true,
                  },
                },
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
          skip: (page - 1) * limit,
          take: limit,
        }),
        db.transaction.count({
          where: whereClause,
        }),
        db.transaction.count(),
      ]);

      return {
        transactions: transactions,
        totalPages: Math.ceil(totalPages / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
      };
    } catch (error) {
      logger.error('Failed to get all transactions', error);
      throw new HttpError('Failed to get all transactions', 400);
    }
  },

  getStaffTransactions: async (staffId: any, query: any) => {
    try {
      const page: number = parseInt(query.page as string) || 1;
      const limit: number = parseInt(query.limit as string) || 10;
      const status = query.status;
      const type = query.type;
      const search = query.search as string;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const singleDate = query.singleDate as string;
      const exportFormat = query.export as string;

      if (status && !transactionStatus.includes(status.toUpperCase())) {
        throw new HttpError('Invalid transaction status', 400);
      }
      if (type && !transactionType.includes(type.toUpperCase())) {
        throw new HttpError('Invalid transaction type', 400);
      }

      const dateFilter = createDateFilter(
        startDate,
        endDate,
        'createdAt',
        singleDate
      );

      const whereClause: any = {
        OR: [
          {
            fromAccount: {
              staffId: Number(staffId),
            },
          },
          {
            toAccount: {
              staffId: Number(staffId),
            },
          },
        ],
        ...(search
          ? {
              AND: [
                {
                  OR: [
                    { reference: { contains: search, mode: 'insensitive' } },
                    { mode: { contains: search, mode: 'insensitive' } },
                  ],
                },
              ],
            }
          : {}),
        ...(status ? { status: status.toUpperCase() } : {}),
        ...(type ? { type: type.toUpperCase() } : {}),
        ...dateFilter,
      };

      // Handle CSV export
      if (exportFormat === 'csv') {
        const transactions = await db.transaction.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          include: {
            fromAccount: {
              select: {
                user: {
                  select: {
                    emailAddress: true,
                    phoneNumber: true,
                    firstName: true,
                    uhid: true,
                  },
                },
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            toAccount: {
              select: {
                user: {
                  select: {
                    emailAddress: true,
                    phoneNumber: true,
                    firstName: true,
                    uhid: true,
                  },
                },
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        });

        const flattenedTransactions = transactions.map((transaction: any) => {
          const baseTransaction = {
            reference: transaction.reference,
            amount: Number(transaction.amount),
            status: transaction.status,
            type: transaction.type,
            mode: transaction.mode,
            remarks: transaction.remarks || 'N/A',
            fromAccountType: transaction.fromAccount?.user
              ? 'User'
              : transaction.fromAccount?.staff
                ? 'Staff'
                : transaction.fromAccount?.system
                  ? 'System'
                  : 'N/A',
            fromAccountName:
              transaction.fromAccount?.user?.firstName ||
              transaction.fromAccount?.staff?.fullName ||
              transaction.fromAccount?.system?.name ||
              'N/A',
            fromAccountIdentifier:
              transaction.fromAccount?.user?.uhid ||
              transaction.fromAccount?.staff?.staffCode ||
              'N/A',
            toAccountType: transaction.toAccount?.user
              ? 'User'
              : transaction.toAccount?.staff
                ? 'Staff'
                : transaction.toAccount?.system
                  ? 'System'
                  : 'N/A',
            toAccountName:
              transaction.toAccount?.user?.firstName ||
              transaction.toAccount?.staff?.fullName ||
              transaction.toAccount?.system?.name ||
              'N/A',
            toAccountIdentifier:
              transaction.toAccount?.user?.uhid ||
              transaction.toAccount?.staff?.staffCode ||
              'N/A',
            date: transaction.createdAt.toLocaleString(),
          };

          // Add creditPaid column only for credit mode transactions
          if (transaction.mode === 'credit') {
            return {
              ...baseTransaction,
              creditPaid: transaction.creditPaid ? 'YES' : 'NO',
            };
          }

          return baseTransaction;
        });

        // Create dynamic headers based on whether any transactions have credit mode
        const hasCreditTransactions = transactions.some(
          (transaction) => transaction.mode === 'credit'
        );
        const baseHeaders = [
          'reference',
          'amount',
          'status',
          'type',
          'mode',
          'remarks',
          'fromAccountType',
          'fromAccountName',
          'fromAccountIdentifier',
          'toAccountType',
          'toAccountName',
          'toAccountIdentifier',
          'date',
        ];

        const headers = hasCreditTransactions
          ? [...baseHeaders.slice(0, 5), 'creditPaid', ...baseHeaders.slice(5)]
          : baseHeaders;

        return createCSVExport(flattenedTransactions, 'staff_transactions', {
          ...CSV_CONFIGS.transactions,
          headers,
        });
      }

      const [transactions, totalPages, totalCount] = await db.$transaction([
        db.transaction.findMany({
          where: whereClause,
          orderBy: { createdAt: 'desc' },
          include: {
            fromAccount: {
              select: {
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            toAccount: {
              select: {
                staff: {
                  select: {
                    fullName: true,
                    staffCode: true,
                  },
                },
                system: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
          skip: (page - 1) * limit,
          take: limit,
        }),
        db.transaction.count({
          where: whereClause,
        }),
        db.transaction.count({
          where: {
            OR: [
              { fromAccount: { staffId: Number(staffId) } },
              { toAccount: { staffId: Number(staffId) } },
            ],
          },
        }),
      ]);

      return {
        transactions: transactions,
        totalPages: Math.ceil(totalPages / limit),
        totalCount: totalCount,
        currentPage: page,
        limit: limit,
      };
    } catch (error) {
      logger.error('Failed to get staff transactions', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get staff transactions', 400);
    }
  },

  staffWithdrawal: async (staffId: any, reqBody: any) => {
    try {
      const { amount, details, password } = reqBody;
      const newAmount = new decimal(amount);
      const charges = newAmount.mul(0.05);
      const checkStaff = await db.staff.findUnique({
        where: { id: Number(staffId) },
        include: { account: true },
      });

      if (!checkStaff) {
        throw new HttpError('Staff does not exist', 400);
      }
      if (new decimal(checkStaff.wallet).lessThan(newAmount)) {
        throw new HttpError('Insufficient funds in wallet for withdrawal', 400);
      }

      const existingWithdrawal = await db.transaction.findFirst({
        where: {
          toAccount: { staffId: Number(staffId) },
          type: 'WITHDRAWAL',
          status: {
            in: ['PENDING', 'PROCESSING'],
          },
        },
      });
      if (existingWithdrawal) {
        throw new HttpError(
          'Pending withdrawal request found. Please wait for it to be processed.',
          400
        );
      }

      const verifyPassword = await bcrypt.compare(
        password,
        checkStaff.password || ''
      );
      if (!verifyPassword) {
        throw new HttpError('The password entered is not the correct', 400);
      }

      const companyAccount = await getMainSystemAccount();

      const transaction = await db.transaction.create({
        data: {
          reference: `WID-${timestamp()}${generateCode(4)?.toUpperCase()}`,
          amount: newAmount,
          charges: charges,
          balance: newAmount.minus(charges),
          mode: 'Withdrawal',
          type: 'WITHDRAWAL',
          status: 'PENDING',
          fromAccountId: companyAccount?.account?.id || null,
          toAccountId: checkStaff.account?.id || null,
          remarks: details,
        },
      });

      // Create notification with socket emission
      await notificationService.createNotificationWithSocket(
        Number(staffId),
        'Withdrawal Request Created',
        `Your withdrawal request of ${newAmount} has been submitted and is pending approval.`,
        'transaction',
        'medium',
        {
          transactionId: transaction.id,
          reference: transaction.reference,
          amount: newAmount.toString(),
          type: 'WITHDRAWAL',
        }
      );

      return { message: 'Withdrawal request created successfully' };
    } catch (error) {
      logger.error('Failed to create withdrawal request', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create withdrawal request', 400);
    }
  },

  staffFundWithdrawalUpdate: async (staffId: any, reqBody: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);
      const { transactionId } = reqBody;

      const canManage = await auth.hasPermission(PERMISSIONS.TRANSACTION_EDIT);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const transaction = await db.transaction.findUnique({
        where: { id: transactionId },
        include: {
          toAccount: {
            include: {
              staff: {
                include: {
                  location: true,
                },
              },
            },
          },
        },
      });

      if (!transaction) {
        throw new HttpError('Transaction not found', 404);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      if (!hasLocationAll) {
        if (hasRegion) {
          const regionId = await auth.getRegionId();
          if (transaction.toAccount?.staff?.location?.regionId !== regionId) {
            throw new HttpError('Unauthorized to update this transaction', 403);
          }
        } else {
          const locationId = await auth.getLocationId();
          if (transaction.toAccount?.staff?.locationId !== locationId) {
            throw new HttpError('Unauthorized to update this transaction', 403);
          }
        }
      }

      // Route to different functions based on transaction type
      if (transaction.type === 'WITHDRAWAL') {
        return await transactionService.handleWithdrawalUpdate(
          staffId,
          reqBody,
          transaction
        );
      } else if (transaction.type === 'REWARD') {
        if (transaction.mode === 'location_voucher') {
          return await transactionService.handleLocationRewardUpdate(
            staffId,
            reqBody,
            transaction
          );
        } else {
          return await transactionService.handleRewardUpdate(
            staffId,
            reqBody,
            transaction
          );
        }
      } else {
        throw new HttpError('Unsupported transaction type', 400);
      }
    } catch (error) {
      logger.error('Failed to update transaction', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update transaction', 400);
    }
  },

  handleWithdrawalUpdate: async (
    staffId: any,
    reqBody: any,
    transaction: any
  ) => {
    const { status: rawStatus, remarks } = reqBody;
    const status = rawStatus?.toUpperCase();
    const { id: transactionId } = transaction;

    const currentStatus = transaction.status;

    if (currentStatus === 'PENDING' && status === 'PROCESSING') {
      const currentWallet = new decimal(
        transaction.toAccount?.staff?.wallet || 0
      );

      if (currentWallet.lessThan(transaction.amount)) {
        await db.transaction.update({
          where: { id: transactionId },
          data: {
            status: 'CANCELLED',
            remarks: `${transaction.remarks || ''}\n - Cancelled: Insufficient wallet balance at processing time`,
          },
        });

        await notificationService.createNotificationWithSocket(
          transaction.toAccount?.staff?.id!,
          'Withdrawal Cancelled',
          'Your withdrawal request has been cancelled due to insufficient wallet balance.',
          'transaction',
          'high',
          {
            transactionId,
            status: 'CANCELLED',
            reason: 'Insufficient wallet balance',
          }
        );
      } else {
        await db.$transaction(async (tx) => {
          await tx.transaction.update({
            where: { id: transactionId },
            data: {
              status: 'PROCESSING',
              remarks: `${transaction.remarks || ''}\n - Processing: ${remarks}`,
            },
          });

          await tx.staff.update({
            where: { id: transaction.toAccount?.staff?.id },
            data: {
              wallet: {
                decrement: transaction.amount,
              },
            },
          });
        });

        await notificationService.createNotificationWithSocket(
          transaction.toAccount?.staff?.id!,
          'Withdrawal Processing',
          `Your withdrawal request of ${transaction.amount} is now being processed.`,
          'transaction',
          'medium',
          {
            transactionId,
            status: 'PROCESSING',
            amount: transaction.amount.toString(),
          }
        );
      }
    } else if (
      currentStatus === 'PROCESSING' &&
      ['FAILED', 'CANCELLED', 'SUCCESS'].includes(status)
    ) {
      if (status !== 'SUCCESS') {
        await db.$transaction(async (tx) => {
          await tx.transaction.update({
            where: { id: transactionId },
            data: {
              status,
              remarks: `${transaction.remarks || ''}\n -${status}: ${remarks}`,
            },
          });

          await tx.staff.update({
            where: { id: transaction.toAccount?.staff?.id },
            data: {
              wallet: {
                increment: transaction.amount,
              },
            },
          });
        });

        sendToUser(
          transaction.toAccount?.staff?.id!,
          'withdrawal_status_updated',
          {
            transactionId,
            status,
            amount: transaction.amount.toString(),
            refunded: true,
            timestamp: new Date(),
          }
        );
      } else {
        await db.transaction.update({
          where: { id: transactionId },
          data: {
            status: 'SUCCESS',
            remarks: `${transaction.remarks || ''}\n - Success: ${remarks}`,
          },
        });

        await notificationService.createNotificationWithSocket(
          transaction.toAccount?.staff?.id!,
          'Withdrawal Completed',
          `Your withdrawal of ${transaction.amount} has been completed successfully.`,
          'transaction',
          'medium',
          {
            transactionId,
            amount: transaction.amount.toString(),
            reference: transaction.reference,
            status: 'SUCCESS',
          }
        );
      }
    } else {
      throw new HttpError(
        `Invalid status transition from ${currentStatus} to ${status}`,
        400
      );
    }

    return { message: 'Withdrawal status updated successfully' };
  },

  handleRewardUpdate: async (staffId: any, reqBody: any, transaction: any) => {
    const { status: rawStatus, remarks } = reqBody;
    const status = rawStatus?.toUpperCase();
    const { id: transactionId } = transaction;

    const updater = await db.staff.findUnique({
      where: { id: Number(staffId) },
      select: { fullName: true },
    });

    if (transaction.status === 'PENDING' && status === 'PROCESSING') {
      let updatedRemarks;
      try {
        const remarksData = JSON.parse(transaction.remarks || '{}');
        remarksData.processingNote = remarks;
        remarksData.processedBy = updater?.fullName || 'Unknown';
        updatedRemarks = JSON.stringify(remarksData);
      } catch {
        updatedRemarks = transaction.remarks;
      }

      await db.transaction.update({
        where: { id: transactionId },
        data: {
          status: 'PROCESSING',
          remarks: updatedRemarks,
        },
      });

      return { message: 'Reward transaction updated to processing' };
    } else if (transaction.status === 'PROCESSING' && status === 'CANCELLED') {
      let updatedRemarks;
      try {
        const remarksData = JSON.parse(transaction.remarks || '{}');
        remarksData.cancellationNote = remarks;
        remarksData.cancelledBy = updater?.fullName || 'Unknown';
        updatedRemarks = JSON.stringify(remarksData);
      } catch {
        updatedRemarks = transaction.remarks;
      }

      await db.transaction.update({
        where: { id: transactionId },
        data: {
          status: 'CANCELLED',
          remarks: updatedRemarks,
        },
      });

      return { message: 'Reward transaction cancelled successfully' };
    } else if (transaction.status === 'PROCESSING' && status === 'SUCCESS') {
      if (remarks) {
        let updatedRemarks;
        try {
          const remarksData = JSON.parse(transaction.remarks || '{}');
          remarksData.successNote = remarks;
          remarksData.completedBy = updater?.fullName || 'Unknown';
          updatedRemarks = JSON.stringify(remarksData);
        } catch {
          updatedRemarks = transaction.remarks;
        }

        await db.transaction.update({
          where: { id: transactionId },
          data: {
            remarks: updatedRemarks,
          },
        });
      }
      return await transactionService.updateLocationVoucherTransaction(
        staffId,
        {
          transactionId,
        }
      );
    } else {
      throw new HttpError(
        'Invalid status transition for reward transaction',
        400
      );
    }
  },

  staffFundTransfer: async (staffId: any, reqBody: any) => {
    try {
      const { amount, toStaffId, password } = reqBody;
      const newAmount = new decimal(amount);
      const checkStaff = await db.staff.findUnique({
        where: { id: Number(staffId) },
        include: { account: true },
      });

      const checReceivingStaff = await db.staff.findUnique({
        where: { staffCode: toStaffId },
        include: { account: true },
      });

      if (!checkStaff) {
        throw new HttpError('Staff does not exist', 400);
      }
      if (!checReceivingStaff) {
        throw new HttpError('receiving staff code appears incorrect', 400);
      }
      if (new decimal(checkStaff.wallet).lessThan(newAmount)) {
        throw new HttpError('Insufficient funds in wallet for transfer', 400);
      }
      const verifyPassword = await bcrypt.compare(
        password,
        checkStaff.password || ''
      );
      if (!verifyPassword) {
        throw new HttpError('The password entered is not the correct', 400);
      }

      const reference = `WID-${timestamp()}${generateCode(4)?.toUpperCase()}`;

      await db.$transaction(async (tx) => {
        await tx.staff.update({
          where: { id: Number(staffId) },
          data: {
            wallet: {
              decrement: newAmount,
            },
          },
        });

        await tx.staff.update({
          where: { staffCode: toStaffId },
          data: {
            wallet: {
              increment: newAmount,
            },
          },
        });
        await tx.transaction.create({
          data: {
            reference,
            amount: newAmount,
            mode: 'internal',
            type: 'TRANSFER',
            status: 'SUCCESS',
            fromAccountId: checkStaff.account?.id || null,
            toAccountId: checReceivingStaff.account?.id || null,
            remarks: 'Staff Internal Fund Transfer',
          },
        });
      });

      // Create notifications with socket emissions for both users
      await notificationService.createNotificationWithSocket(
        Number(staffId),
        'Transfer Sent',
        `You have successfully transferred ${newAmount} to ${checReceivingStaff.fullName}.`,
        'transaction',
        'medium',
        {
          reference,
          amount: newAmount.toString(),
          recipient: checReceivingStaff.fullName,
          recipientCode: toStaffId,
          type: 'TRANSFER_SENT',
        }
      );

      await notificationService.createNotificationWithSocket(
        checReceivingStaff.id,
        'Transfer Received',
        `You have received ${newAmount} from ${checkStaff.fullName}.`,
        'transaction',
        'medium',
        {
          reference,
          amount: newAmount.toString(),
          sender: checkStaff.fullName,
          senderCode: checkStaff.staffCode,
          type: 'TRANSFER_RECEIVED',
        }
      );

      const mailOptions = {
        from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
        to: checkStaff.email,
        subject: 'Wallet Transfer Notification',
        template: 'transfer-notification',
        context: {
          title: `You have received a wallet transfer from ${checkStaff.fullName}`,
          recipient_name: checReceivingStaff.fullName,
          account: 'wallet',
          amount: newAmount,
          reference,
          timestamp: dayjs(new Date()).format('DD MMMM YYYY, h:mm:ss A'),
        },
      };
      enqueueSendEmailJob(mailOptions);
      return { message: 'Transfer successful' };
    } catch (error) {
      logger.error('Failed to transfer funds', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to transfer funds', 400);
    }
  },

  transferToVoucher: async (staffId: any, reqBody: any) => {
    try {
      const { amount, password } = reqBody;
      const newAmount = new decimal(amount);
      const checkStaff = await db.staff.findUnique({
        where: { id: Number(staffId) },
        include: { account: true },
      });

      if (!checkStaff) {
        throw new HttpError('Staff does not exist', 400);
      }

      if (new decimal(checkStaff.wallet).lessThan(newAmount)) {
        throw new HttpError('Insufficient funds in wallet for transfer', 400);
      }
      const verifyPassword = await bcrypt.compare(
        password,
        checkStaff.password || ''
      );
      if (!verifyPassword) {
        throw new HttpError('The password entered is not the correct', 400);
      }

      const reference = `WID-${timestamp()}${generateCode(4)?.toUpperCase()}`;

      await db.$transaction(async (tx) => {
        await tx.staff.update({
          where: { id: Number(staffId) },
          data: {
            wallet: {
              decrement: newAmount,
            },
            mealVoucher: {
              increment: newAmount,
            },
          },
        });
        await tx.transaction.create({
          data: {
            reference,
            amount: newAmount,
            mode: 'internal',
            type: 'TRANSFER',
            status: 'SUCCESS',
            fromAccountId: checkStaff.account?.id || null,
            toAccountId: checkStaff.account?.id || null,
            remarks: 'Transfer from wallet to meal voucher',
          },
        });
      });

      return { message: 'Transfer successful' };
    } catch (error) {
      logger.error('Failed to transfer funds', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to transfer funds', 400);
    }
  },

  // Create meal voucher transaction with PENDING status
  sendVoucher: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.TRANSACTION_CREATE);

      const { staffIds, amount, description } = reqBody;

      const requestingStaff = await db.staff.findUnique({
        where: { id: Number(staffId) },
        select: { fullName: true },
      });

      if (!staffIds || !Array.isArray(staffIds) || staffIds.length === 0) {
        throw new HttpError(
          'Staff IDs are required and must be a non-empty array',
          400
        );
      }

      if (!amount || amount <= 0) {
        throw new HttpError('Amount must be greater than 0', 400);
      }

      const voucherAmount = new decimal(amount);
      const reference = `VOU-${timestamp()}${generateCode(4)?.toUpperCase()}`;

      // Get system account for vouchers
      const systemAccount = await getMainSystemAccount();
      if (!systemAccount || !systemAccount.account) {
        throw new HttpError('System account not found', 500);
      }

      // Get recipient staff details
      const recipients = await db.staff.findMany({
        where: {
          id: { in: staffIds.map((id) => Number(id)) },
          isActive: true,
        },
        select: {
          id: true,
          fullName: true,
          locationId: true,
        },
      });

      if (recipients.length === 0) {
        throw new HttpError('No active staff found with the provided IDs', 404);
      }

      // Check if some staff IDs were not found
      const foundIds = recipients.map((r) => r.id);
      const notFoundIds = staffIds.filter(
        (id) => !foundIds.includes(Number(id))
      );

      if (notFoundIds.length > 0) {
        throw new HttpError(
          `Staff not found or inactive: ${notFoundIds.join(', ')}`,
          404
        );
      }

      // Save staff data in remarks as JSON
      const staffData = recipients.map((r) => ({ id: r.id, name: r.fullName }));
      const remarksData = {
        description: description || '',
        requestedBy: requestingStaff?.fullName,
        staffData,
        individualAmount: voucherAmount,
      };

      // Calculate total amount (individual amount * number of recipients)
      const totalAmount = voucherAmount.mul(recipients.length);

      // Create transaction with PENDING status
      const transaction = await db.transaction.create({
        data: {
          reference,
          amount: totalAmount,
          type: 'REWARD',
          status: 'PENDING',
          mode: 'voucher',
          remarks: JSON.stringify(remarksData),
          fromAccountId: systemAccount.account.id,
          toAccountId: null,
          locationId: recipients[0]?.locationId,
        },
      });

      // Get staff with CAFETERIA_SPECIAL_APPROVE permission
      const staffWithApprovalPermission = await db.staff.findMany({
        where: {
          isActive: true,
          roles: {
            some: {
              permissions: {
                some: {
                  action: PERMISSIONS.CAFETERIA_SPECIAL_APPROVE,
                },
              },
            },
          },
        },
        select: {
          id: true,
          fullName: true,
        },
      });

      // Send notifications to all staff with approval permission
      if (staffWithApprovalPermission.length > 0) {
        const staffIds = staffWithApprovalPermission.map((staff) => staff.id);

        // Send notifications directly without permission check
        const notifications = staffIds.map((id) => ({
          staffId: id,
          title: 'Meal Voucher Credit',
          message: `Meal voucher credit of ${totalAmount} is been requested for. Description: ${description} for ${recipients.length} staff at ${voucherAmount} each.`,
          type: 'meal_voucher',
          priority: 'high',
        }));

        await db.notification.createMany({
          data: notifications,
        });
      }

      return {
        message: 'Meal voucher transaction created with PENDING status',
        transactionId: transaction.id,
        reference,
        individualAmount: voucherAmount.toString(),
        totalAmount: totalAmount.toString(),
        recipientCount: recipients.length,
        recipients: recipients.map((r) => ({
          id: r.id,
          name: r.fullName,
          locationId: r.locationId,
        })),
      };
    } catch (error) {
      logger.error('Failed to create meal voucher transaction', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create meal voucher transaction', 400);
    }
  },

  // Update voucher transaction to SUCCESS and award meal vouchers
  updateVoucherTransaction: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.TRANSACTION_EDIT);

      const { transactionId } = reqBody;

      if (!transactionId) {
        throw new HttpError('Transaction ID is required', 400);
      }

      // Get the pending transaction
      const transaction = await db.transaction.findUnique({
        where: { id: transactionId },
      });

      if (!transaction) {
        throw new HttpError('Transaction not found', 404);
      }

      if (transaction.status !== 'PROCESSING') {
        throw new HttpError('Transaction is not in PROCESSING status', 400);
      }

      // Extract staff data from remarks
      let staffData, individualAmount;
      try {
        const remarksData = JSON.parse(transaction.remarks || '{}');
        staffData = remarksData.staffData || [];
        individualAmount = new decimal(remarksData.individualAmount || 0);
      } catch {
        throw new HttpError('Invalid transaction data', 400);
      }

      if (!staffData.length) {
        throw new HttpError('No staff data found in transaction', 400);
      }

      // Get system account
      const systemAccount = await getMainSystemAccount();
      if (!systemAccount || !systemAccount.account) {
        throw new HttpError('System account not found', 500);
      }

      const operations = [];

      // Update main transaction status to CONFIRMED
      operations.push(
        db.transaction.update({
          where: { id: transactionId },
          data: {
            status: 'CONFIRMED',
          },
        })
      );

      // Award meal vouchers and create individual transactions
      const description = JSON.parse(transaction.remarks || '{}').description;

      const emailQueue = [];

      for (const staff of staffData) {
        // Update staff meal voucher
        operations.push(
          db.staff.update({
            where: { id: staff.id },
            data: { mealVoucher: { increment: individualAmount } },
          })
        );

        const staffAccount = await db.account.findFirst({
          where: { staffId: staff.id },
          select: {
            id: true,
            staff: {
              select: { staffCode: true, email: true, fullName: true },
            },
          },
        });

        if (staffAccount && staffAccount.staff) {
          const reference = `${transaction.reference}-${staffAccount.staff.staffCode}`;

          operations.push(
            db.transaction.create({
              data: {
                reference,
                amount: individualAmount,
                type: 'REWARD',
                status: 'SUCCESS',
                mode: 'Voucher',
                remarks: `Meal voucher credit - ${individualAmount} (from ${transaction.reference}). Description: ${description}`,
                fromAccountId: systemAccount.account.id,
                toAccountId: staffAccount.id,
                locationId: transaction.locationId,
              },
            })
          );

          // Store email data for later
          emailQueue.push({
            email: staffAccount.staff.email,
            name: staffAccount.staff.fullName,
            reference,
          });
        }
      }

      // Run all DB operations in one atomic transaction
      await db.$transaction(operations);

      // Now queue emails after successful commit
      for (const email of emailQueue) {
        enqueueSendEmailJob({
          from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
          to: email.email,
          subject: 'Meal Voucher Credit Notification',
          template: 'transfer-notification',
          context: {
            title: `You have received a meal voucher credit. Description: ${description}`,
            recipient_name: email.name,
            account: 'Meal Voucher',
            amount: individualAmount,
            reference: email.reference,
            timestamp: dayjs(new Date()).format('DD MMMM YYYY, h:mm:ss A'),
          },
        });
      }

      const staffIds = staffData.map((s: any) => s.id);

      // Send notifications to all recipients
      if (staffIds.length > 0) {
        await notificationService.sendBulkNotification(Number(staffId), {
          userIds: staffIds,
          title: 'Meal Voucher Received',
          message: `You have received a meal voucher of ${individualAmount} for ${description}.`,
          type: 'meal_voucher',
          priority: 'medium',
        });
      }

      return {
        message: `Meal voucher awarded successfully to ${staffIds.length} recipient(s)`,
        reference: transaction.reference,
        individualAmount: individualAmount.toString(),
        totalAmount: transaction.amount.toString(),
        recipientCount: staffIds.length,
      };
    } catch (error) {
      logger.error('Failed to update voucher transaction', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update voucher transaction', 400);
    }
  },

  // Send meal voucher to all staff in specific locations with PENDING status
  sendLocationVoucher: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.TRANSACTION_CREATE);

      const { locationIds, amount, description, staffTypes } = reqBody;

      const requestingStaff = await db.staff.findUnique({
        where: { id: Number(staffId) },
        select: { fullName: true },
      });

      if (
        !locationIds ||
        !Array.isArray(locationIds) ||
        locationIds.length === 0
      ) {
        throw new HttpError(
          'Location IDs are required and must be a non-empty array',
          400
        );
      }

      if (!amount || amount <= 0) {
        throw new HttpError('Amount must be greater than 0', 400);
      }

      const voucherAmount = new decimal(amount);
      const reference = `VOU-${timestamp()}${generateCode(4)?.toUpperCase()}`;

      // Get system account for vouchers
      const systemAccount = await getMainSystemAccount();
      if (!systemAccount || !systemAccount.account) {
        throw new HttpError('System account not found', 500);
      }

      // Verify locations exist
      const locations = await db.location.findMany({
        where: {
          id: { in: locationIds.map((id) => Number(id)) },
        },
        select: {
          id: true,
          name: true,
        },
      });

      if (locations.length === 0) {
        throw new HttpError(
          'No valid locations found with the provided IDs',
          404
        );
      }

      const foundLocationIds = locations.map((n) => n.id);
      const notFoundLocationIds = locationIds.filter(
        (id) => !foundLocationIds.includes(Number(id))
      );

      if (notFoundLocationIds.length > 0) {
        throw new HttpError(
          `Locations not found: ${notFoundLocationIds.join(', ')}`,
          404
        );
      }

      // Build staff type filter
      let staffTypeFilter: any = {};
      if (staffTypes) {
        const typeConditions = [];
        if (staffTypes.fullTime) typeConditions.push('FULL');
        if (staffTypes.intern) typeConditions.push('INTERN');
        if (staffTypes.contract) typeConditions.push('CONTRACT');

        if (typeConditions.length > 0) {
          staffTypeFilter = { type: { in: typeConditions } };
        }
      }

      // Get all active staff in the specified locations with staff type filter
      const recipients = await db.staff.findMany({
        where: {
          locationId: { in: foundLocationIds },
          isActive: true,
          ...staffTypeFilter,
        },
        select: {
          id: true,
          fullName: true,
          locationId: true,
          type: true,
        },
      });

      if (recipients.length === 0) {
        throw new HttpError(
          'No active staff found in the specified locations with selected staff types',
          404
        );
      }

      // Save location and staff data in remarks as JSON
      const locationData = locations.map((l) => ({ id: l.id, name: l.name }));
      const staffData = recipients.map((r) => ({
        id: r.id,
        name: r.fullName,
        type: r.type,
      }));
      const remarksData = {
        description: description || '',
        requestedBy: requestingStaff?.fullName,
        locationData,
        staffData,
        staffTypes,
        individualAmount: voucherAmount,
      };

      // Calculate total amount (individual amount * number of recipients)
      const totalAmount = voucherAmount.mul(recipients.length);

      // Create transaction with PENDING status
      const transaction = await db.transaction.create({
        data: {
          reference,
          amount: totalAmount,
          type: 'REWARD',
          status: 'PENDING',
          mode: 'voucher',
          remarks: JSON.stringify(remarksData),
          fromAccountId: systemAccount.account.id,
          toAccountId: null,
          locationId: foundLocationIds[0],
        },
      });

      // Get staff with CAFETERIA_SPECIAL_APPROVE permission
      const staffWithApprovalPermission = await db.staff.findMany({
        where: {
          isActive: true,
          roles: {
            some: {
              permissions: {
                some: {
                  action: PERMISSIONS.CAFETERIA_SPECIAL_APPROVE,
                },
              },
            },
          },
        },
        select: {
          id: true,
          fullName: true,
        },
      });

      // Send notifications to all staff with approval permission
      if (staffWithApprovalPermission.length > 0) {
        const approverIds = staffWithApprovalPermission.map(
          (staff) => staff.id
        );
        const locationNames = locations.map((l) => l.name).join(', ');
        const staffTypeNames = staffTypes
          ? Object.entries(staffTypes)
              .filter(([_, value]) => value)
              .map(([key]) => key)
              .join(', ')
          : 'All types';

        const notifications = approverIds.map((id) => ({
          staffId: id,
          title: 'Location Meal Voucher Credit',
          message: `Location meal voucher credit of ${totalAmount} requested for ${recipients.length} staff (${staffTypeNames}) at ${locationNames}. Amount: ${voucherAmount} each. Description: ${description}`,
          type: 'meal_voucher',
          priority: 'high',
        }));

        await db.notification.createMany({
          data: notifications,
        });
      }

      return {
        message:
          'Location meal voucher transaction created with PENDING status',
        transactionId: transaction.id,
        reference,
        individualAmount: voucherAmount.toString(),
        totalAmount: totalAmount.toString(),
        recipientCount: recipients.length,
        locations: locationData,
        staffTypes: staffTypes || 'All types',
      };
    } catch (error) {
      logger.error('Failed to create location meal voucher transaction', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError(
        'Failed to create location meal voucher transaction',
        400
      );
    }
  },

  // Update location voucher transaction to SUCCESS and award meal vouchers
  updateLocationVoucherTransaction: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.TRANSACTION_EDIT);

      const { transactionId } = reqBody;

      if (!transactionId) {
        throw new HttpError('Transaction ID is required', 400);
      }

      const transaction = await db.transaction.findUnique({
        where: { id: transactionId },
      });

      if (!transaction) {
        throw new HttpError('Transaction not found', 404);
      }

      if (transaction.status !== 'PROCESSING') {
        throw new HttpError('Transaction is not in PROCESSING status', 400);
      }

      // Extract data from remarks
      let staffData, individualAmount, description;
      try {
        const remarksData = JSON.parse(transaction.remarks || '{}');
        staffData = remarksData.staffData || [];
        individualAmount = new decimal(remarksData.individualAmount || 0);
        description = remarksData.description || '';
      } catch {
        throw new HttpError('Invalid transaction data', 400);
      }

      if (!staffData.length) {
        throw new HttpError('No staff data found in transaction', 400);
      }

      const systemAccount = await getMainSystemAccount();
      if (!systemAccount || !systemAccount.account) {
        throw new HttpError('System account not found', 500);
      }

      const operations = [];

      // Update main transaction status to SUCCESS
      operations.push(
        db.transaction.update({
          where: { id: transactionId },
          data: { status: 'SUCCESS' },
        })
      );

      const emailQueue = [];

      // Award meal vouchers and create individual transactions
      for (const staff of staffData) {
        operations.push(
          db.staff.update({
            where: { id: staff.id },
            data: { mealVoucher: { increment: individualAmount } },
          })
        );

        const staffAccount = await db.account.findFirst({
          where: { staffId: staff.id },
          select: {
            id: true,
            staff: {
              select: { staffCode: true, email: true, fullName: true },
            },
          },
        });

        if (staffAccount && staffAccount.staff) {
          const reference = `${transaction.reference}-${staffAccount.staff.staffCode}`;

          operations.push(
            db.transaction.create({
              data: {
                reference,
                amount: individualAmount,
                type: 'REWARD',
                status: 'SUCCESS',
                mode: 'Voucher',
                remarks: `Location meal voucher credit - ${individualAmount} (from ${transaction.reference}). Description: ${description}`,
                fromAccountId: systemAccount.account.id,
                toAccountId: staffAccount.id,
                locationId: transaction.locationId,
              },
            })
          );

          emailQueue.push({
            email: staffAccount.staff.email,
            name: staffAccount.staff.fullName,
            reference,
          });
        }
      }

      await db.$transaction(operations);

      // Queue emails
      for (const email of emailQueue) {
        enqueueSendEmailJob({
          from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
          to: email.email,
          subject: 'Location Meal Voucher Credit Notification',
          template: 'transfer-notification',
          context: {
            title: `You have received a location meal voucher credit. Description: ${description}`,
            recipient_name: email.name,
            account: 'Meal Voucher',
            amount: individualAmount,
            reference: email.reference,
            timestamp: dayjs(new Date()).format('DD MMMM YYYY, h:mm:ss A'),
          },
        });
      }

      const staffIds = staffData.map((s: any) => s.id);

      if (staffIds.length > 0) {
        await notificationService.sendBulkNotification(Number(staffId), {
          userIds: staffIds,
          title: 'Location Meal Voucher Received',
          message: `You have received a location meal voucher of ${individualAmount} for ${description}.`,
          type: 'meal_voucher',
          priority: 'medium',
        });
      }

      return {
        message: `Location meal voucher awarded successfully to ${staffIds.length} recipient(s)`,
        reference: transaction.reference,
        individualAmount: individualAmount.toString(),
        totalAmount: transaction.amount.toString(),
        recipientCount: staffIds.length,
      };
    } catch (error) {
      logger.error('Failed to update location voucher transaction', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update location voucher transaction', 400);
    }
  },

  handleLocationRewardUpdate: async (
    staffId: any,
    reqBody: any,
    transaction: any
  ) => {
    const { status: rawStatus, remarks } = reqBody;
    const status = rawStatus?.toUpperCase();
    const { id: transactionId } = transaction;

    const updater = await db.staff.findUnique({
      where: { id: Number(staffId) },
      select: { fullName: true },
    });

    if (transaction.status === 'PENDING' && status === 'PROCESSING') {
      let updatedRemarks;
      try {
        const remarksData = JSON.parse(transaction.remarks || '{}');
        remarksData.processingNote = remarks;
        remarksData.processedBy = updater?.fullName || 'Unknown';
        updatedRemarks = JSON.stringify(remarksData);
      } catch {
        updatedRemarks = transaction.remarks;
      }

      await db.transaction.update({
        where: { id: transactionId },
        data: {
          status: 'PROCESSING',
          remarks: updatedRemarks,
        },
      });

      return { message: 'Location reward transaction updated to processing' };
    } else if (transaction.status === 'PROCESSING' && status === 'CANCELLED') {
      let updatedRemarks;
      try {
        const remarksData = JSON.parse(transaction.remarks || '{}');
        remarksData.cancellationNote = remarks;
        remarksData.cancelledBy = updater?.fullName || 'Unknown';
        updatedRemarks = JSON.stringify(remarksData);
      } catch {
        updatedRemarks = transaction.remarks;
      }

      await db.transaction.update({
        where: { id: transactionId },
        data: {
          status: 'CANCELLED',
          remarks: updatedRemarks,
        },
      });

      return { message: 'Location reward transaction cancelled successfully' };
    } else if (transaction.status === 'PROCESSING' && status === 'SUCCESS') {
      if (remarks) {
        let updatedRemarks;
        try {
          const remarksData = JSON.parse(transaction.remarks || '{}');
          remarksData.successNote = remarks;
          remarksData.completedBy = updater?.fullName || 'Unknown';
          updatedRemarks = JSON.stringify(remarksData);
        } catch {
          updatedRemarks = transaction.remarks;
        }

        await db.transaction.update({
          where: { id: transactionId },
          data: {
            remarks: updatedRemarks,
          },
        });
      }
      return await transactionService.updateLocationVoucherTransaction(
        staffId,
        {
          transactionId,
        }
      );
    } else {
      throw new HttpError(
        'Invalid status transition for location reward transaction',
        400
      );
    }
  },
};
