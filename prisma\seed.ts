import { db } from '../src/v1/utils/model';
import { enqueueSendEmailJob } from '../src/v1/jobs/queueJobs/queues/emailQueueJob';
import * as fs from 'fs';
import * as path from 'path';

import * as bcrypt from 'bcryptjs';

async function main() {
  // const permissions = [
  //   'dashboard:view',

  //   'package:view',
  //   'package:create',
  //   'package:edit',
  //   'package:delete',

  //   'location:create',
  //   'location:view',
  //   'location:edit',
  //   'location:region',
  //   'location:all',

  //   'incident:create',
  //   'incident:view',
  //   'incident:edit',
  //   'incident:submit',
  //   'incident:approve',
  //   'incident:delete',
  //   'incident:comment',
  //   'incident:assign',
  //   'incident:close',

  //   'reward:view',
  //   'reward:create',
  //   'reward:edit',
  //   'reward:delete',

  //   'referral:view',
  //   'referral:create',
  //   'referral:edit',
  //   'referral:delete',

  //   'transaction:view',
  //   'transaction:create',
  //   'transaction:edit',

  //   'staff:create',
  //   'staff:edit',
  //   'staff:edit_own',
  //   'staff:view',

  //   'feedback:view',

  //   'settings:view',
  //   'settings:create',
  //   'settings:edit',
  //   'settings:delete',

  //   'requisition:create',
  //   'requisition:view',
  //   'requisition:edit',
  //   'requisition:submit',
  //   'requisition:approve',
  //   'requisition:delete',
  //   'requisition:cancel',

  //   'forum:view',
  //   'forum:create_group ',
  //   'forum:view_group',
  //   'forum:edit_group',
  //   'forum:delete_group',
  //   'forum:add_members',
  //   'forum:remove_members',
  //   'forum:join_group',
  //   'forum:send_message',
  //   'forum:view_messages',
  //   'forum:edit_message',
  //   'forum:delete_message',
  //   'forum:moderate_messages',
  //   'forum:pin_message',

  //   'patient:view',
  //   'patient:create',
  //   'patient:edit',
  //   'patient:delete',

  //   'appointment:view',
  //   'appointment:create',
  //   'appointment:edit',
  //   'appointment:delete',
  //   'appointment:reschedule',
  //   'appointment:cancel',

  //   'role:view',
  //   'role:create',
  //   'role:edit',
  //   'role:delete',

  //   'permission:view',
  //   'permission:create',
  //   'permission:edit',
  //   'permission:delete',

  //   'medical_record:view',
  //   'medical_record:create',
  //   'medical_record:edit',

  //   'doctor:view',
  //   'doctor:create',
  //   'doctor:edit',

  //   'hub:view',
  //   'hub:create',
  //   'hub:edit',

  //   'process:view',
  //   'process:create',
  //   'process:edit',

  //   'interaction:view',
  //   'interaction:create',
  //   'interaction:edit',
  //   'interaction:reply',

  //   'cafeteria:view',
  //   'cafeteria:inventory_manage',
  //   'cafeteria:menu_manage',
  //   'cafeteria:orders_manage',
  //   'cafeteria:pos_access',
  //   'cafeteria:special_approve',

  //   'chis:view',
  //   'chis:create',
  //   'chis:edit',
  //   'chis:approve',
  //   'chis:generate_code',

  //   'ai_assistant:view',
  //   'ai_assistant:chat',
  // ];

  // for (const permission of permissions) {
  //   await db.permission.upsert({
  //     where: { action: permission },
  //     update: {},
  //     create: {
  //       action: permission,
  //     },
  //   });
  // }

  // const permissionIds = await db.permission.findMany({
  //   select: { id: true },
  // });

  // const superAdminRole = await db.role.upsert({
  //   where: { name: 'superadmin' },
  //   update: {
  //     permissions: {
  //       set: [],
  //       connect: permissionIds,
  //     },
  //   },
  //   create: {
  //     name: 'Superadmin',
  //     description: 'Has full access to manage all resources',
  //     permissions: {
  //       connect: permissionIds,
  //     },
  //   },
  // });

  try {
    const activeStaff = await db.staff.findMany({
      where: {
        isActive: true,
        locked: false
      },
    });

    for (const staff of activeStaff) {
      await db.staff.update({
        where: { id: staff.id },
        data: { locked: true },
      });
      // const name = staff.fullName.split(' ')[0].toLowerCase();
      // const sentenceCaseName = name.charAt(0).toUpperCase() + name.slice(1);
      // const mailOptions = {
      //   from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
      //   to: staff.email,
      //   subject: `Welcome to the final quarter of 2025!`,
      //   template: 'coms',
      //   context: {
      //     name: sentenceCaseName,
      //   },
      //   attachments: [
      //     {
      //       filename: 'CHL-IS Guide.pdf',
      //       path: path.join(process.cwd(), 'uploads', 'CHL-IS Guide.pdf'),
      //     },
      //   ],
      // };
      // enqueueSendEmailJob(mailOptions);
    }
  } catch (error) {
    console.log('Error encountered:', error);
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await db.$disconnect();
  });
