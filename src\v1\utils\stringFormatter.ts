const formatSlug = (input: string) => {
  return input
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .trim()
    .replace(/\s+/g, '-');
};

const trimString = (str: string) => {
  return str.trim();
};

const formatEmail = (str: string) => {
  return str.toLowerCase().trim();
};

const formatUpperCase = (str: string) => {
  return str.toUpperCase().trim();
};

const formatEnumValue = (enumValue: string) => {
  return enumValue
    .toLowerCase()
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export const formatString = {
  formatSlug,
  trimString,
  formatEmail,
  formatUpperCase,
  formatEnumValue,
};
