import { db, decimal } from '../../utils/model';
import * as bcrypt from 'bcryptjs';
import crypto from 'crypto';
import jwt, { Secret } from 'jsonwebtoken';
import config from '../../../config/app.config';
import { HttpError } from '../../utils/httpError';
import { messagingService } from '../messaging';
import { formatString } from '../../utils/stringFormatter';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';
import { getInitials } from '../../utils/util';
import { createDateFilter, timestamp, generateCode } from '../../utils/util';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import { toTitleCase } from '../../utils/util';
import { getCafeteriaSystemAccount } from '../system';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../../utils/cache';
import { broadcast, sendToUser } from '../socket';
import { logger } from '../../utils/logger';
import { notificationService } from '../notification';
import * as fs from 'fs';
import * as path from 'path';

export const SECRET_KEY = config.SIGNING_TOKEN_SECRET as Secret;

// Helper function to clear all admin-related caches
export const clearStaffCaches = async (): Promise<void> => {
  await deleteCacheByPattern('staff:*');
};

const getOrderStats = (orders: Array<{ totalAmount?: any }> = []) => {
  const stats = orders.reduce(
    (acc, order) => {
      const amount = Number(order.totalAmount ?? 0);
      acc.total += amount;
      acc.min = Math.min(acc.min, amount);
      acc.max = Math.max(acc.max, amount);
      acc.count += 1;
      return acc;
    },
    { total: 0, min: Infinity, max: -Infinity, count: 0 }
  );

  // Handle empty array safely
  if (stats.count === 0) {
    return { total: 0, min: 0, max: 0, count: 0 };
  }

  return stats;
};

export const staffService = {
  getAllStaff: async (staffId: number, query: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);
      const canManage = await auth.hasPermission(PERMISSIONS.STAFF_VIEW);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (hasLocationAll) {
        // No location filter - get all data
      } else if (hasRegion) {
        const regionId = await auth.getRegionId();
        locationFilter = {
          location: {
            regionId,
          },
        };
      } else {
        const locationId = await auth.getLocationId();
        locationFilter = { locationId };
      }

      const page: number = parseInt(query.page as string);
      const limit: number = parseInt(query.limit as string);
      const search: string = (query.search as string) || '';
      const status = query.status;
      const startDate = query.startDate as string;
      const endDate = query.endDate as string;
      const singleDate = query.singleDate as string;
      const dateFilter = createDateFilter(
        startDate,
        endDate,
        'createdAt',
        singleDate
      );

      const skip = page && limit ? (page - 1) * limit : undefined;
      const take = limit ? limit : undefined;

      const whereClause: any = {
        ...(search
          ? {
              OR: [
                { fullName: { contains: search, mode: 'insensitive' } },
                { email: { contains: search, mode: 'insensitive' } },
                { staffCode: { contains: search } },
                {
                  referralCode: {
                    code: { contains: search, mode: 'insensitive' },
                  },
                },
                {
                  location: { name: { contains: search, mode: 'insensitive' } },
                },
                {
                  department: {
                    name: { contains: search, mode: 'insensitive' },
                  },
                },
                { unit: { name: { contains: search, mode: 'insensitive' } } },
              ],
            }
          : {}),
        ...locationFilter,
        ...dateFilter,
        ...(typeof status === 'string' && status.trim().toLowerCase() === 'true'
          ? { isActive: true }
          : typeof status === 'string' &&
              status.trim().toLowerCase() === 'false'
            ? { isActive: false }
            : {}),
      };

      const [staffs, totalPages, totalCount] = await db.$transaction([
        db.staff.findMany({
          orderBy: { createdAt: 'desc' },
          ...(skip !== undefined && { skip }),
          ...(take !== undefined && { take }),
          where: whereClause,
          include: {
            referralCode: {
              select: {
                id: true,
                code: true,
                isActive: true,
                _count: {
                  select: {
                    referralUsages: true,
                  },
                },
              },
            },
            roles: {
              select: {
                name: true,
                permissions: {
                  select: {
                    action: true,
                  },
                },
              },
            },
            location: {
              select: {
                name: true,
                region: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            doctorProfile: {
              select: {
                isDoctor: true,
                isConsultant: true,
                isVisitingConsultant: true,
                privilegingDocumentStatus: true,
                privileged: true,
                privilegingDocumentRemarks: true,
                privilegingDocumentUrl: true,
                specialty: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            department: {
              select: {
                name: true,
              },
            },
            unit: {
              select: {
                name: true,
              },
            },
            account: true,
            cafeteriaOrders: {
              where: {
                paymentType: 'CREDIT',
                creditPaid: false,
              },
              select: {
                totalAmount: true,
              },
            },
            blacklist: true,
          },
        }),
        db.staff.count({
          where: whereClause,
        }),
        db.staff.count(),
      ]);

      const discountWithPriceCount = staffs.map(
        ({ password, cafeteriaOrders, ...staff }) => {
          const unpaidCredit = cafeteriaOrders.reduce((total, order) => {
            return total + Number(order.totalAmount);
          }, 0);

          return {
            ...staff,
            codeUsage: staff.referralCode
              ? staff.referralCode._count.referralUsages
              : 0,
            unpaidCredit,
          };
        }
      );

      return {
        staffs: discountWithPriceCount,
        totalPages: Math.ceil(totalPages / limit),
        totalCount: totalCount,
      };
    } catch (error) {
      logger.error('Error getting all staff:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch staff', 400);
    }
  },

  createStaff: async (staffId: number, reqBody: any) => {
    try {
      const {
        email,
        staffCode,
        fullName,
        locationId,
        departmentId,
        type,
        phoneNumber,
        referralRewardIds,
        staffRewardIds,
        roleIds,
        isDoctor,
        specialtyId,
        isConsultant,
        isVisitingConsultant,
        ...rest
      } = reqBody;

      // Check location permissions
      await staffHasPermission(staffId, PERMISSIONS.STAFF_CREATE);
      const auth = createStaffAuthHelper(staffId);
      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      if (!hasLocationAll) {
        if (hasRegion) {
          const regionId = await auth.getRegionId();
          const targetLocation = await db.location.findUnique({
            where: { id: Number(locationId) },
            select: { regionId: true },
          });
          if (!targetLocation || targetLocation.regionId !== regionId) {
            throw new HttpError('Cannot create staff for this location', 403);
          }
        } else {
          const userLocationId = await auth.getLocationId();
          if (Number(locationId) !== userLocationId) {
            throw new HttpError('Cannot create staff for this location', 403);
          }
        }
      }

      if (!roleIds || roleIds.length === 0) {
        throw new HttpError('At least one role is required', 400);
      }

      const formattedString = formatString.trimString(staffCode);

      const existingStaffCode = await db.staff.findUnique({
        where: { staffCode: formattedString },
      });

      if (existingStaffCode) {
        throw new HttpError('Staff code already exists', 400);
      }

      const existingStaff = await db.staff.findFirst({
        where: {
          OR: [
            { email: formatString.formatEmail(email) },
            { fullName: fullName.trim() },
          ],
        },
      });

      if (existingStaff) {
        throw new HttpError(
          'Staff with this email or name already exists',
          400
        );
      }

      const refCode = getInitials(fullName, phoneNumber);
      const staffCreateData: any = {
        data: {
          ...rest,
          fullName: fullName.trim(),
          type: type,
          locationId: Number(locationId),
          departmentId: Number(departmentId),
          phoneNumber,
          email: formatString.formatEmail(email),
          staffCode: formattedString,
          referralCode: {
            create: {
              code: refCode,
            },
          },
          account: {
            create: {
              type: 'STAFF',
            },
          },
        },
      };

      if (referralRewardIds && referralRewardIds.length > 0) {
        staffCreateData.data.referralCode.create.reward = {
          connect: referralRewardIds.map((id: string | number) => ({
            id: Number(id),
          })),
        };
      }

      if (staffRewardIds && staffRewardIds.length > 0) {
        staffCreateData.data.eligibleRewards = {
          connect: staffRewardIds.map((id: string | number) => ({
            id: Number(id),
          })),
        };
      }

      staffCreateData.data.roles = {
        connect: roleIds.map((id: string | number) => ({ id: Number(id) })),
      };

      const createdStaff = await db.staff.create(staffCreateData);

      if (isDoctor) {
        const doctorProfileData: any = {
          staffId: createdStaff.id,
          isDoctor,
          isConsultant,
          isVisitingConsultant,
        };

        if (specialtyId) {
          doctorProfileData.specialtyId = Number(specialtyId);
        }

        await db.doctorProfile.create({
          data: doctorProfileData,
        });
      }

      return {
        message: 'Staff created successfully',
      };
    } catch (error) {
      logger.error('Error creating staff:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create staff', 400);
    }
  },

  adminUpdateStaff: async (staffId: number, reqBody: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);
      const canManage = await auth.hasPermission(PERMISSIONS.STAFF_EDIT);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (!hasLocationAll) {
        if (hasRegion) {
          const regionId = await auth.getRegionId();
          locationFilter = {
            location: {
              regionId,
            },
          };
        } else {
          const locationId = await auth.getLocationId();
          locationFilter = { locationId };
        }
      }

      // Extract account and staff fields
      const {
        id,
        roleIds,
        isActive,
        creditLimit,
        mealVoucher,
        isDoctor,
        specialtyId,
        ...dataToUpdate
      } = reqBody;

      const checkStaff = await db.staff.findUnique({
        where: {
          id: Number(id),
          ...locationFilter,
        },
        include: {
          referralCode: true,
          roles: true,
          account: true,
          doctorProfile: true,
        },
      });
      if (!checkStaff) {
        throw new HttpError('Staff does not exist', 400);
      }

      // Filter out doctorProfile fields from staff update
      const doctorFields = [
        'isConsultant',
        'isVisitingConsultant',
        'privilegingDocumentRemarks',
        'privilegingDocumentStatus',
      ];
      const updateData: any = {
        ...Object.fromEntries(
          Object.entries(dataToUpdate).filter(
            ([key]) => !doctorFields.includes(key)
          )
        ),
      };

      // Handle roles update if provided
      if (roleIds && Array.isArray(roleIds)) {
        updateData.roles = {
          set: roleIds.map((roleId: number) => ({ id: Number(roleId) })),
        };
      }
      if (creditLimit !== undefined)
        updateData.creditLimit = new decimal(creditLimit);

      if (mealVoucher !== undefined)
        updateData.mealVoucher = new decimal(mealVoucher);

      if (typeof isActive === 'boolean') {
        updateData.isActive = isActive;
      }

      await db.staff.update({
        where: { id: Number(id) },
        data: updateData,
      });

      // Handle doctorProfile updates
      const hasDoctorUpdates =
        isDoctor !== undefined ||
        specialtyId !== undefined ||
        doctorFields.some((field) => dataToUpdate[field] !== undefined);

      if (hasDoctorUpdates) {
        // Validate privilegingDocumentStatus enum
        const validStatuses = [
          'SUBMITTED',
          'UNDER_REVIEW',
          'APPROVED',
          'REJECTED',
        ];
        if (
          dataToUpdate.privilegingDocumentStatus &&
          !validStatuses.includes(dataToUpdate.privilegingDocumentStatus)
        ) {
          throw new HttpError('Invalid privileging document status', 400);
        }

        if (isDoctor && !checkStaff.doctorProfile) {
          // Create new doctor profile
          const doctorProfileData: any = {
            staffId: Number(id),
            isDoctor: true,
            ...Object.fromEntries(
              Object.entries(dataToUpdate).filter(([key]) =>
                doctorFields.includes(key)
              )
            ),
          };
          if (specialtyId) doctorProfileData.specialtyId = Number(specialtyId);
          if (dataToUpdate.privilegingDocumentStatus === 'APPROVED')
            doctorProfileData.privileged = true;

          await db.doctorProfile.create({ data: doctorProfileData });
        } else if (checkStaff.doctorProfile) {
          // Update existing doctor profile
          const doctorUpdateData: any = {
            ...Object.fromEntries(
              Object.entries(dataToUpdate).filter(([key]) =>
                doctorFields.includes(key)
              )
            ),
          };
          if (isDoctor !== undefined) doctorUpdateData.isDoctor = isDoctor;
          if (specialtyId !== undefined)
            doctorUpdateData.specialtyId = specialtyId
              ? Number(specialtyId)
              : null;
          if (dataToUpdate.privilegingDocumentStatus === 'APPROVED')
            doctorUpdateData.privileged = true;

          await db.doctorProfile.update({
            where: { staffId: Number(id) },
            data: doctorUpdateData,
          });

          // Send notification if privilegingDocumentStatus is updated
          if (dataToUpdate.privilegingDocumentStatus) {
            await notificationService.createNotificationWithSocket(
              Number(id),
              'Privileging Document Status Updated',
              `Your privileging document status has been updated to: ${dataToUpdate.privilegingDocumentStatus}`,
              'privileging',
              'medium'
            );
          }
        }
      }

      if (typeof isActive === 'boolean' && checkStaff.referralCode) {
        await db.referralCode.update({
          where: { id: checkStaff.referralCode.id },
          data: { isActive },
        });
      }

      await clearStaffCaches();

      return {
        message: 'Staff updated successfully',
      };
    } catch (error) {
      logger.error('Error updating staff:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update staff', 400);
    }
  },

  verifyStaffCode: async (reqBody: any) => {
    try {
      const refCode = formatString.trimString(reqBody.code);
      const checkStaffCode = await db.referralCode.findUnique({
        where: { code: refCode },
      });
      if (!checkStaffCode) {
        throw new HttpError('Referral code does not exist', 400);
      }

      if (!checkStaffCode.isActive) {
        throw new HttpError('Referral code has been deactivated', 400);
      }

      const packageExist = await db.package.findUnique({
        where: {
          id: Number(reqBody.id),
        },
        select: {
          name: true,
          slug: true,
          description: true,
        },
      });
      if (!packageExist) {
        throw new HttpError('Package does not exist', 400);
      }

      return {
        package: packageExist,
        message: 'Referral link generated successfully',
      };
    } catch (error) {
      logger.error('Error verifying staff code:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to verify staff code', 400);
    }
  },

  updateStaff: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT_OWN);
      const { currentPassword, ...dataToUpdate } = reqBody;

      const checkStaff = await db.staff.findUnique({
        where: { id: Number(staffId) },
      });
      if (!checkStaff) {
        throw new HttpError('Staff does not exist', 400);
      }

      // Handle password change if requested
      if (reqBody.password) {
        const isPasswordValid = await bcrypt.compare(
          currentPassword,
          checkStaff.password || ''
        );
        if (!isPasswordValid) {
          throw new HttpError('Enter a valid current password', 400);
        }
        dataToUpdate.password = await bcrypt.hash(reqBody.password, 10);
      }

      await db.staff.update({
        where: { id: Number(staffId) },
        data: dataToUpdate,
      });

      await clearStaffCaches();

      // Emit socket notification for profile update
      sendToUser(Number(staffId), 'profile_updated', {
        staffId: Number(staffId),
        updatedBy: staffId,
        timestamp: new Date(),
      });

      return {
        message: 'Account updated successfully',
      };
    } catch (error) {
      logger.error('Error updating account:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update account', 400);
    }
  },

  verifyStaffId: async (staffId: any, reqBody: any) => {
    try {
      const { staffCode } = reqBody;
      await staffHasPermission(staffId, PERMISSIONS.CAFETERIA_ORDERS_MANAGE);
      const formattedCode = formatString.trimString(staffCode);

      const checkStaffExist = await db.staff.findFirst({
        where: { staffCode: formattedCode },
        include: {
          cafeteriaOrders: {
            where: {
              paymentType: 'CREDIT',
              creditPaid: false,
            },
            select: {
              totalAmount: true,
            },
          },
        },
      });

      if (!checkStaffExist) {
        throw new HttpError(
          'Staff does not exist, Please check the ID entered',
          400
        );
      }

      if (checkStaffExist.isActive === false) {
        throw new HttpError(
          'Sorry! This account has been deactivated. Please contact the admin.',
          400
        );
      }

      const { total, min, max, count } = getOrderStats(checkStaffExist.cafeteriaOrders);

      return {
        name: checkStaffExist.fullName,
        unpaidOrderStats: {
          outstandingCredit: total,
          min,
          max,
          count,
        },
      };
    } catch (error) {
      logger.error('Error checking staff code:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to check staff code', 400);
    }
  },

  getStaffProfile: async (staffId: number) => {
    try {
      const cacheKey = `staff:profile:${staffId}`;

      // const cachedProfile = await getCache(cacheKey);
      // if (cachedProfile) {
      //   devLog(`Admin profile for ${adminId} retrieved from cache`);
      //   return cachedProfile;
      // }

      const result = await db.staff.findUnique({
        where: { id: Number(staffId) },
        include: {
          roles: {
            include: {
              permissions: {
                select: {
                  action: true,
                },
              },
            },
          },
          referralCode: {
            select: {
              code: true,
              _count: {
                select: {
                  referralUsages: true,
                },
              },
            },
          },
          location: {
            select: {
              id: true,
              name: true,
            },
          },
          department: {
            select: {
              name: true,
            },
          },
          doctorProfile: {
            include: {
              specialty: {
                select: {
                  name: true,
                },
              },
            },
          },
          cafeteriaOrders: {
            where: {
              paymentType: 'CREDIT',
              creditPaid: false,
            },
            select: {
              totalAmount: true,
            },
          },
        },
      });

      if (!result) {
        throw new HttpError('Staff account not found', 404);
      }

      const { password, locationId, departmentId, cafeteriaOrders, ...rest } =
        result;

        const { total, min, max, count } = getOrderStats(cafeteriaOrders);

      // await setCache(cacheKey, rest);

      return {
        ...rest,
        codeUsage: result.referralCode?._count.referralUsages,
        outstandingCredit: total,
        unpaidOrderStats: {
          min,
          max,
          count,
        },
      };
    } catch (error) {
      logger.error('Error getting admin profile:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch admin profile', 400);
    }
  },

  forgotPassword: async (reqBody: any) => {
    try {
      const { code } = reqBody;
      const staff = await db.staff.findUnique({
        where: { staffCode: code },
      });
      if (!staff) {
        throw new HttpError('Staff account cannot be found', 400);
      }
      if (!staff.isActive) {
        throw new HttpError('Staff account is deactivated', 400);
      }
      const name = staff.fullName.split(' ')[0];
      const password = crypto.randomBytes(4).toString('hex');
      await db.staff.update({
        where: { id: staff.id },
        data: { password: bcrypt.hashSync(password, 10) },
      });
      const mailOptions = {
        from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
        to: staff.email,
        subject: 'Your staff login password',
        template: 'staff-login',
        context: {
          name: toTitleCase(name),
          password: password,
        },
      };
      enqueueSendEmailJob(mailOptions);
      return {
        message: `A new password sent succesfully to your email address - ${staff.email}`,
      };
    } catch (error) {
      logger.error('Error resetting password:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to reset password', 400);
    }
  },

  checkStaffId: async (reqBody: any) => {
    try {
      const { code } = reqBody;
      const staff = await db.staff.findUnique({
        where: { staffCode: code },
      });
      if (!staff) {
        throw new HttpError('Staff account cannot be found', 400);
      }
      if (!staff.isActive) {
        throw new HttpError('Staff account is deactivated', 400);
      }
      if (staff && staff.password && !staff.lastLogin) {
        return {
          message: ` Please check your email - ${staff.email} for password`,
          statusCode: 201,
        };
      }
      if (!staff.password) {
        const name = staff.fullName.split(' ')[0];
        const password = crypto.randomBytes(4).toString('hex');
        await db.staff.update({
          where: { id: staff.id },
          data: { password: bcrypt.hashSync(password, 10) },
        });
        const mailOptions = {
          from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
          to: staff.email,
          subject: 'Your staff login password',
          template: 'staff-login',
          context: {
            name: toTitleCase(name),
            password: password,
          },
        };
        enqueueSendEmailJob(mailOptions);
        return {
          message: `Password sent succesfully to your email address - ${staff.email}`,
          statusCode: 201,
        };
      }
      return { message: 'Staff ID verified' };
    } catch (error) {
      logger.error('Error verifying staff ID:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to verify staff ID', 400);
    }
  },

  loginStaff: async (reqBody: any) => {
    try {
      const { code, password } = reqBody;
      const staff = await db.staff.findUnique({
        where: { staffCode: code },
      });
      if (!staff || !staff.isActive) {
        throw new HttpError('Invalid credentials', 400);
      }
      const isPasswordValid = await bcrypt.compare(
        password,
        staff.password || ''
      );
      if (!isPasswordValid) {
        throw new HttpError('Enter a valid password', 400);
      }
      const token = jwt.sign({ id: staff.id }, SECRET_KEY, {
        expiresIn: '7 days',
      });
      await db.staff.update({
        where: { id: staff.id },
        data: { lastLogin: new Date() },
      });

      // Emit socket notification for staff login
      sendToUser(staff.id, 'login_successful', {
        staffId: staff.id,
        fullName: staff.fullName,
        lastLogin: new Date(),
        timestamp: new Date(),
      });

      return {
        sub: staff.id,
        token: token,
      };
    } catch (error) {
      logger.error('Error logging in staff:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to login staff', 400);
    }
  },

  listRole: async (staffId: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.ROLE_VIEW);
      return db.role.findMany({
        include: {
          permissions: true,
        },
      });
    } catch (error) {
      logger.error('Error listing roles:', error);
      throw new HttpError('Failed to list roles', 400);
    }
  },

  getConsultants: async () => {
    try {
      const result = await db.staff.findMany({
        where: {
          isActive: true,
          doctorProfile: {
            isDoctor: true,
            isConsultant: true,
          },
        },
        select: {
          id: true,
          fullName: true,
        },
      });

      return result.map(({ fullName, ...rest }) => ({
        ...rest,
        name: fullName,
      }));
    } catch (error) {
      logger.error('Error fetching consultants:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch consultants', 400);
    }
  },

  payCredit: async (staffId: number, reqBody: any) => {
    try {
      
      const { staffCode, reference, amount, mode, creditedBy } = reqBody;
      const creditAmount = amount ? new decimal(amount) : new decimal(0);

      // Find the staff member to credit
      const targetStaff = await db.staff.findUnique({
        where: { staffCode: formatString.trimString(staffCode) },
        include: {
          account: true,
          cafeteriaOrders: {
            where: {
              paymentType: 'CREDIT',
              creditPaid: false,
            },
            orderBy: {
              createdAt: 'asc',
            },
            select: {
              id: true,
              orderNumber: true,
              totalAmount: true,
              createdAt: true,
            },
          },
        },
      });

      if (!reference) {
        await staffHasPermission(staffId, PERMISSIONS.CAFETERIA_ORDERS_MANAGE);
      }

      if (!targetStaff || !targetStaff.isActive) {
        throw new HttpError('Staff member not found or inactive', 404);
      }

      // Get system account for transactions
      const systemAccount = await getCafeteriaSystemAccount();
      if (!systemAccount || !systemAccount.account) {
        throw new HttpError('System account not found', 500);
      }

      if (!amount || !mode) {
        throw new HttpError('Amount and mode are required', 400);
      }

      const transactionReference = `VOU-${timestamp()}${generateCode(4)?.toUpperCase()}`;
      if (creditAmount.lte(0)) {
        throw new HttpError('Credit amount must be greater than 0', 400);
      }

      const existingMealVoucher = new decimal(targetStaff.mealVoucher || 0);

      // Add amount to meal voucher first
      const newMealVoucherBalance = existingMealVoucher.add(creditAmount);

      // Calculate which orders can be cleared (chronologically from oldest to newest, but prioritizing smallest amounts within each date)
      const ordersWithDate = targetStaff.cafeteriaOrders.map(order => ({
        ...order,
        dateOnly: new Date(order.createdAt || new Date()).toDateString()
      }));

      // Group orders by date
      const ordersByDate = ordersWithDate.reduce((groups, order) => {
        const dateKey = order.dateOnly;
        if (!groups[dateKey]) {
          groups[dateKey] = [];
        }
        groups[dateKey].push(order);
        return groups;
      }, {} as Record<string, typeof ordersWithDate>);

      // Sort dates chronologically and within each date, sort by amount (smallest first)
      const sortedDates = Object.keys(ordersByDate).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

      let availableVoucher = newMealVoucherBalance;
      let ordersToUpdate: string[] = [];
      let voucherUsed = new decimal(0);
      let totalOrdersCleared = new decimal(0);

      // Process each date chronologically
      for (const date of sortedDates) {
        // Sort orders within this date by amount (smallest first)
        const ordersForDate = ordersByDate[date].sort((a, b) =>
          new decimal(a.totalAmount).cmp(new decimal(b.totalAmount))
        );

        // Try to clear orders from smallest to largest within this date
        for (const order of ordersForDate) {
          const orderAmount = new decimal(order.totalAmount);
          if (availableVoucher.gte(orderAmount)) {
            ordersToUpdate.push(order.id);
            availableVoucher = availableVoucher.sub(orderAmount);
            voucherUsed = voucherUsed.add(orderAmount);
            totalOrdersCleared = totalOrdersCleared.add(orderAmount);
          }
        }
      }
      
      const finalMealVoucherBalance = availableVoucher;

      // Execute the transaction
      await db.$transaction(async (tx) => {
        // Update meal voucher balance
        await tx.staff.update({
          where: { id: targetStaff.id },
          data: { mealVoucher: finalMealVoucherBalance },
        });

        // Create transaction record for the payment
        await tx.transaction.create({
          data: {
            reference: reference ? reference : transactionReference,
            amount: creditAmount,
            type: 'PURCHASE',
            status: 'SUCCESS',
            mode: mode,
            remarks: `Meal voucher credited by: ${creditedBy}`,
            fromAccountId: targetStaff.account?.id || null,
            toAccountId: systemAccount.account?.id || null,
            locationId: targetStaff.locationId,
          },
        });

        if (ordersToUpdate.length > 0) {
          const orderNumbers = targetStaff.cafeteriaOrders
            .filter(order => ordersToUpdate.includes(order.id))
            .map(order => order.orderNumber);

          await Promise.all([
            tx.cafeteriaOrder.updateMany({
              where: { id: { in: ordersToUpdate } },
              data: { creditPaid: true, creditPaymentMethod: 'MEAL VOUCHER' },
            }),
            tx.transaction.updateMany({
              where: { reference: { in: orderNumbers } },
              data: { creditPaid: true, creditPaymentMethod: 'VOUCHER' },
            }),
          ]);
        }
      });

      await notificationService.createNotificationWithSocket(
        Number(targetStaff.id),
        'Meal Voucher Credited',
        `You have received a meal voucher of ${creditAmount} from ${creditedBy} ${ordersToUpdate.length > 0 ? 'to clear unpaid credit.' : ''}`,
        'transaction',
        'medium',
        {
          reference,
          amount: creditAmount,
          type: mode,
        }
      );

      return {
        message: 'Credit payment processed successfully',
        amountAdded: creditAmount.toString(),
        ordersCleared: ordersToUpdate.length,
        totalOrdersCleared: totalOrdersCleared.toString(),
        remainingMealVoucher: finalMealVoucherBalance.toString(),
      };
    } catch (error) {
      logger.error('Error processing credit payment:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to process credit payment', 500);
    }
  },

  ceditStaffMealVoucher: async (staffId: number, reqBody: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.CAFETERIA_ORDERS_MANAGE);

      const { staffCode, amount, mode, creditedBy } = reqBody;
      const voucherAmount = new decimal(amount);

      const formattedCode = formatString.trimString(staffCode);

      const staffExist = await db.staff.findUnique({
        where: { staffCode: formattedCode },
        include: {
          account: true,
        },
      });

      if (!staffExist) {
        throw new HttpError('Staff does not exist', 400);
      }

      if (voucherAmount.lte(0)) {
        throw new HttpError('Amount must be greater than 0', 400);
      }

      const reference = `VOU-${timestamp()}${generateCode(4)?.toUpperCase()}`;

      const systemAccount = await getCafeteriaSystemAccount();
      if (!systemAccount || !systemAccount.account) {
        throw new HttpError('System account not found', 500);
      }

      await db.$transaction(async (tx) => {
        await tx.staff.update({
          where: { staffCode: formattedCode },
          data: {
            mealVoucher: {
              increment: voucherAmount,
            },
          },
        });

        await tx.transaction.create({
          data: {
            reference,
            amount: voucherAmount,
            type: 'PURCHASE',
            status: 'SUCCESS',
            mode: mode,
            remarks: `Meal voucher credited by - ${creditedBy}`,
            fromAccountId: systemAccount.account?.id || null,
            toAccountId: staffExist.account?.id || null,
            locationId: staffExist.locationId,
          },
        });
      });

      await notificationService.createNotificationWithSocket(
        Number(staffExist.id),
        'Meal Voucher Credited',
        `You have received a meal voucher of ${voucherAmount} from ${creditedBy}.`,
        'transaction',
        'medium',
        {
          reference,
          amount: voucherAmount,
          type: mode,
        }
      );

      return {
        message: `Meal voucher sent successfully`,
      };
    } catch (error) {
      logger.error('Failed to send meal voucher', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to send meal voucher', 400);
    }
  },

  blacklistStaff: async (staffId: number, reqBody: any) => {
    const { targetStaffId, action, reason, blockedBy } = reqBody;
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT);
      const auth = createStaffAuthHelper(staffId);
      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      const staff = await db.staff.findUnique({
        where: { id: Number(targetStaffId) },
        select: {
          location: {
            select: { id: true },
          },
          blacklist: true,
        },
      });
      if (!staff) {
        throw new HttpError('Staff not found', 404);
      }
      const isBlacklistedForAction = staff.blacklist.some(
        (entry) => entry.isActive && entry.action === action
      );

      if (isBlacklistedForAction) {
        throw new HttpError('Staff is already blocked for this action', 400);
      }

      if (!hasLocationAll) {
        if (hasRegion) {
          const regionId = await auth.getRegionId();
          const targetLocation = await db.location.findUnique({
            where: { id: Number(staff.location.id) },
            select: { regionId: true },
          });
          if (!targetLocation || targetLocation.regionId !== regionId) {
            throw new HttpError('Cannot block staff for this location', 403);
          }
        } else {
          const userLocationId = await auth.getLocationId();
          if (Number(staff.location.id) !== userLocationId) {
            throw new HttpError('Cannot block staff for this location', 403);
          }
        }
      }
      await db.blacklist.create({
        data: {
          staffId: Number(targetStaffId),
          action: action,
          reason,
          blockedBy,
        },
      });

      return {
        message: `Staff blocked successfully`,
      };
    } catch (error) {
      logger.error('Failed to block staff', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to block staff', 400);
    }
  },

  unBlacklistStaff: async (staffId: number, reqBody: any) => {
    const { targetStaffId, blacklistId, unblockedBy } = reqBody;
    try {
      await staffHasPermission(staffId, PERMISSIONS.STAFF_EDIT);
      const auth = createStaffAuthHelper(staffId);
      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      const staff = await db.staff.findUnique({
        where: { id: Number(targetStaffId) },
        select: {
          location: {
            select: { id: true },
          },
          blacklist: true,
        },
      });
      if (!staff) {
        throw new HttpError('Staff not found', 404);
      }

      const blacklistEntry = await db.blacklist.findUnique({
        where: { id: Number(blacklistId) },
      });
      if (!blacklistEntry) {
        throw new HttpError('Blacklist entry not found', 404);
      }
      if (!blacklistEntry.isActive) {
        throw new HttpError('Already unblocked', 400);
      }

      if (!hasLocationAll) {
        if (hasRegion) {
          const regionId = await auth.getRegionId();
          const targetLocation = await db.location.findUnique({
            where: { id: Number(staff.location.id) },
            select: { regionId: true },
          });
          if (!targetLocation || targetLocation.regionId !== regionId) {
            throw new HttpError('Cannot block staff for this location', 403);
          }
        } else {
          const userLocationId = await auth.getLocationId();
          if (Number(staff.location.id) !== userLocationId) {
            throw new HttpError('Cannot block staff for this location', 403);
          }
        }
      }
      await db.blacklist.update({
        where: { id: Number(blacklistId) },
        data: {
          unblockedAt: new Date(),
          isActive: false,
          unblockedBy,
        },
      });

      return {
        message: `Staff unblocked successfully for ${blacklistEntry.action}`,
      };
    } catch (error) {
      logger.error('Failed to unblock staff', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to unblock staff', 400);
    }
  },
  doctorPracticePrivilege: async (
    staffId: any,
    filePath: any,
    reqBody: any
  ) => {
    try {
      const { originalName } = reqBody;

      const doctor = await db.staff.findUnique({
        where: { id: Number(staffId) },
        include: {
          doctorProfile: {
            include: {
              specialty: { select: { name: true } },
            },
          },
        },
      });

      if (!doctor?.doctorProfile?.isConsultant) {
        throw new HttpError(
          'Doctor profile not found or not a consultant',
          404
        );
      }

      let documentPath = null;
      if (filePath) {
        const privilegingDir = path.join(
          process.cwd(),
          'uploads',
          'doctors_privileging'
        );
        if (!fs.existsSync(privilegingDir)) {
          fs.mkdirSync(privilegingDir, { recursive: true });
        }

        // Delete existing document if it exists
        if (doctor.doctorProfile.privilegingDocumentUrl) {
          const existingFilePath = path.join(
            process.cwd(),
            doctor.doctorProfile.privilegingDocumentUrl
          );
          if (fs.existsSync(existingFilePath)) {
            fs.unlinkSync(existingFilePath);
          }
        }

        const doctorName = formatString.formatSlug(doctor.fullName);
        const ext = path.extname(originalName);
        const fileName = `privileging_${doctorName}${ext}`;
        const fullPath = path.join(privilegingDir, fileName);
        fs.copyFileSync(filePath, fullPath);
        documentPath = `uploads/doctors_privileging/${fileName}`;
      }

      await db.doctorProfile.update({
        where: { staffId: Number(staffId) },
        data: {
          privilegingDocumentStatus: 'SUBMITTED',
          privilegingDocumentUrl: documentPath,
        },
      });

      // Send email to authorities with PDF attachment
      if (documentPath) {
        const mailOptions = {
          from: '"Cedarcrest Hospitals Innovations" <<EMAIL>>',
          to: process.env.NEXT_PUBLIC_PRIVILEGING_EMAIL,
          subject: `Doctor Practice Privilege Application - ${doctor.fullName}`,
          template: 'privileging',
          context: {
            title: 'Doctor Practice Privilege Application',
            message: `Dr. ${doctor.fullName} (${doctor.doctorProfile.specialty?.name || 'N/A'}) has submitted their practice privilege application.`,
            doctorName: doctor.fullName,
            specialty: doctor.doctorProfile.specialty?.name || 'N/A',
            submissionDate: new Date().toLocaleDateString(),
          },
          attachments: [
            {
              filename: originalName,
              path: path.join(process.cwd(), documentPath),
            },
          ],
        };
        enqueueSendEmailJob(mailOptions);
      }

      return { message: 'Practice privilege document uploaded successfully' };
    } catch (error) {
      logger.error('Error uploading practice privilege document:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to upload practice privilege document', 400);
    }
  },
};
