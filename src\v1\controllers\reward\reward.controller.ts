import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { rewardService } from '../../services/reward/reward';

const ListRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(rewardService.getAllRewards, req.query, res, staffId);
};

const UpdateRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(rewardService.updateReward, req.body, res, staffId);
};

const CreateRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(rewardService.createReward, req.body, res, staffId);
};

const VerifyReferralCodeHandler = (req: Request, res: Response) => {
  controllerOperations(
    rewardService.verifyReferralCode,
    undefined,
    res,
    req.body
  );
};

const DeleteRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { rewardId } = req.params;
  controllerOperations(
    rewardService.deleteReward,
    undefined,
    res,
    staffId,
    parseInt(rewardId)
  );
};

// Statistics and History Handlers
const GetRewardStatisticsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    rewardService.getRewardStatistics,
    req.query,
    res,
    staffId
  );
};

const GetRewardHistoryHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(rewardService.getRewardHistory, req.query, res, staffId);
};

// Simple handlers only - no complex analytics or automatic calculations needed

export const rewardControllers = {
  // Core reward management (simple and clean)
  ListRewardHandler,
  CreateRewardHandler,
  UpdateRewardHandler,
  DeleteRewardHandler,
  VerifyReferralCodeHandler,
  GetRewardStatisticsHandler,
  GetRewardHistoryHandler,
};
