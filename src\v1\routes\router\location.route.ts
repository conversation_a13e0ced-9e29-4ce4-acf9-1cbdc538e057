import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { locationControllers } from '../../controllers/location/location.controller';

export const locationRoute = Router();

//Category routes
locationRoute.post(
  '/new-location',
  secure,
  locationControllers.CreateLocationHandler
);
locationRoute.get('/list', locationControllers.ListLocationHandler);
locationRoute.patch(
  '/update',
  secure,
  locationControllers.UpdateLocationHandler
);
locationRoute.delete(
  '/delete/:locationId',
  secure,
  locationControllers.DeleteLocationHandler
);
